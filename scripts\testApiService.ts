import { mongoApiService } from '../src/utils/mongoApiService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Test MongoDB API Service
async function testApiService() {
  console.log('🔍 Testing MongoDB API Service...');
  
  try {
    // Initialize API service
    await mongoApiService.initialize();
    console.log('✅ Initialized API Service successfully!');
    
    // Get health status
    const healthStatus = await mongoApiService.getHealthStatus();
    console.log('📊 API Service Health Status:', JSON.stringify(healthStatus, null, 2));
    
    // Test account ID
    const testAccountId = (process.env.FACEBOOK_ACCOUNT_IDS || 'act_123456789').split(',')[0];
    console.log(`📊 Using test account: ${testAccountId}`);
    
    // Get campaigns
    console.log('🔍 Getting campaigns...');
    const campaigns = await mongoApiService.getCampaigns(testAccountId);
    console.log(`📊 Found ${campaigns.length} campaigns`);
    
    if (campaigns.length > 0) {
      console.log('📊 Sample campaign:', JSON.stringify(campaigns[0], null, 2));
      
      // Get campaign insights
      console.log(`🔍 Getting insights for campaign: ${campaigns[0].id}`);
      const insights = await mongoApiService.getInsights(campaigns[0].id, 'campaign', 'last_7d');
      console.log(`📊 Found ${insights.length} insights`);
      
      if (insights.length > 0) {
        console.log('📊 Sample insight:', JSON.stringify(insights[0], null, 2));
      }
      
      // Get performance summary
      console.log(`🔍 Getting performance summary for campaign: ${campaigns[0].id}`);
      const summary = await mongoApiService.getPerformanceSummary(
        testAccountId,
        'campaign',
        campaigns[0].id,
        'last_7d'
      );
      
      console.log('📊 Performance Summary:', JSON.stringify(summary, null, 2));
    }
    
    // Get account overview
    console.log('🔍 Getting account overview...');
    const overview = await mongoApiService.getAccountOverview(testAccountId);
    console.log('📊 Account Overview:', JSON.stringify(overview, null, 2));
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ API Service test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testApiService();
