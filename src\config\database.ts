// Database configuration
export const databaseConfig = {
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017',
    database: process.env.DATABASE_NAME || 'facebook_ads_dashboard',
    options: {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferMaxEntries: 0,
      bufferCommands: false,
    }
  },
  
  sync: {
    intervalHours: parseInt(process.env.SYNC_INTERVAL_HOURS || '1'),
    maxDaysHistory: parseInt(process.env.MAX_DAYS_HISTORY || '30'),
    enabledTypes: (process.env.ENABLED_SYNC_TYPES || 'campaigns,adsets,ads').split(','),
    batchSize: parseInt(process.env.SYNC_BATCH_SIZE || '100'),
    rateLimitDelay: parseInt(process.env.RATE_LIMIT_DELAY || '100'), // ms between API calls
  },
  
  cache: {
    ttl: parseInt(process.env.CACHE_TTL || '300'), // 5 minutes
    maxSize: parseInt(process.env.CACHE_MAX_SIZE || '1000'),
  }
};

// Environment validation
export const validateEnvironment = () => {
  const required = [
    'MONGODB_URI',
    'FACEBOOK_APP_ID',
    'FACEBOOK_APP_SECRET'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  console.log('✅ Environment validation passed');
};

// Database health check
export const checkDatabaseHealth = async () => {
  try {
    const { mongoDbService } = await import('../utils/mongoDbService');
    await mongoDbService.connect();
    const health = await mongoDbService.getHealthCheck();
    await mongoDbService.disconnect();
    
    return {
      status: 'healthy',
      ...health
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    };
  }
};
