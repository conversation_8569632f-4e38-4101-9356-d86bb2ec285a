export interface FacebookUser {
  id: string;
  name: string;
  email?: string;
  picture?: {
    data: {
      url: string;
    };
  };
}

export interface AdAccount {
  id: string;
  name: string;
  account_id: string;
  currency: string;
  account_status: number;
  business?: {
    id: string;
    name: string;
  };
}

export interface Campaign {
  id: string;
  name: string;
  status: string;
  objective: string;
  created_time: string;
  updated_time: string;
  start_time?: string;
  stop_time?: string;
  budget_remaining?: string;
  daily_budget?: string;
  lifetime_budget?: string;
}

export interface AdSet {
  id: string;
  name: string;
  status: string;
  campaign_id: string;
  created_time: string;
  updated_time: string;
  start_time?: string;
  end_time?: string;
  daily_budget?: string;
  lifetime_budget?: string;
  bid_strategy: string;
  optimization_goal: string;
}

export interface Ad {
  id: string;
  name: string;
  status: string;
  adset_id: string;
  campaign_id: string;
  created_time: string;
  updated_time: string;
  creative?: {
    id: string;
    name: string;
    object_story_spec?: any;
  };
}

export interface Insights {
  date_start: string;
  date_stop: string;
  impressions: string;
  clicks: string;
  spend: string;
  reach: string;
  frequency: string;
  cpm: string;
  cpc: string;
  ctr: string;
  cpp: string;
  cost_per_unique_click: string;
  unique_clicks: string;
  unique_ctr: string;
  actions?: Array<{
    action_type: string;
    value: string;
  }>;
  cost_per_action_type?: Array<{
    action_type: string;
    value: string;
  }>;
}

export interface FacebookApiResponse<T> {
  data: T[];
  paging?: {
    cursors: {
      before: string;
      after: string;
    };
    next?: string;
    previous?: string;
  };
}

// Business Manager Types
export interface BusinessManager {
  id: string;
  name: string;
  primary_page?: {
    id: string;
    name: string;
  };
  created_time: string;
  updated_time: string;
  verification_status: 'not_verified' | 'verified' | 'pending';
  is_hidden: boolean;
  link: string;
  timezone_id: number;
  two_factor_type: string;
  user_access_expire_time?: string;
  created_by?: {
    id: string;
    name: string;
  };
  owned_ad_accounts?: AdAccount[];
  owned_pages?: Page[];
  owned_pixels?: Pixel[];
  owned_apps?: App[];
}

export interface BusinessUser {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'EMPLOYEE' | 'FINANCE_EDITOR' | 'FINANCE_ANALYST' | 'ADS_RIGHTS_REVIEWER';
  pending: boolean;
  status: 'CONFIRMED' | 'PENDING' | 'DECLINED';
  invited_by?: string;
  invite_link?: string;
  permissions: string[];
}

export interface Page {
  id: string;
  name: string;
  category: string;
  category_list?: Array<{
    id: string;
    name: string;
  }>;
  tasks?: string[];
  access_token?: string;
  can_post: boolean;
  fan_count?: number;
  link: string;
  picture?: {
    data: {
      url: string;
    };
  };
  verification_status?: string;
  is_published: boolean;
}

export interface Pixel {
  id: string;
  name: string;
  creation_time: string;
  last_fired_time?: string;
  code: string;
  is_created_by_business: boolean;
  owner_business?: {
    id: string;
    name: string;
  };
  owner_ad_account?: {
    id: string;
    name: string;
  };
  is_unavailable: boolean;
  data_use_setting: 'EMPTY' | 'ADVERTISING_AND_ANALYTICS' | 'ANALYTICS_ONLY';
  enable_automatic_matching: boolean;
  first_party_cookie_status: 'EMPTY' | 'FIRST_PARTY_COOKIE_ENABLED' | 'FIRST_PARTY_COOKIE_DISABLED';
  is_restricted_use: boolean;
  matched_domain_uri?: string;
}

export interface App {
  id: string;
  name: string;
  namespace?: string;
  icon_url?: string;
  logo_url?: string;
  daily_active_users?: string;
  weekly_active_users?: string;
  monthly_active_users?: string;
  category: string;
  subcategory?: string;
  link: string;
  supported_platforms: string[];
  app_type: number;
  object_store_urls?: {
    [key: string]: string;
  };
}

export interface AssetSharingRequest {
  id: string;
  requestor_id: string;
  owner_id: string;
  asset_id: string;
  asset_type: 'AD_ACCOUNT' | 'PAGE' | 'PIXEL' | 'APP';
  status: 'PENDING' | 'APPROVED' | 'DECLINED';
  message?: string;
  created_time: string;
  updated_time: string;
}

export interface BusinessRole {
  id: string;
  name: string;
  permissions: string[];
}

// Extended AdAccount interface for BM integration
export interface ExtendedAdAccount extends AdAccount {
  business_manager?: BusinessManager;
  access_type: 'OWNER' | 'AGENCY';
  permitted_roles: string[];
  tasks: string[];
  tos_accepted?: {
    [key: string]: number;
  };
  funding_source?: string;
  funding_source_details?: {
    id: string;
    display_string: string;
    type: number;
  };
  is_notifications_enabled: boolean;
  spend_cap?: string;
  amount_spent?: string;
  balance?: string;
  disable_reason?: number;
  end_advertiser?: string;
  media_agency?: string;
  partner?: string;
  rf_spec?: any;
  show_checkout_experience?: boolean;
  tax_id_status?: number;
  tax_id_type?: string;
  tax_id?: string;
}