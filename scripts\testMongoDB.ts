import { mongoDbService } from '../src/utils/mongoDbService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Test MongoDB connection
async function testMongoDBConnection() {
  console.log('🔍 Testing MongoDB connection...');
  console.log(`MongoDB URI: ${process.env.MONGODB_URI || 'mongodb://localhost:27017'}`);
  console.log(`Database: ${process.env.DATABASE_NAME || 'facebook_ads_dashboard'}`);
  
  try {
    // Connect to MongoDB
    await mongoDbService.connect();
    console.log('✅ Connected to MongoDB successfully!');
    
    // Create indexes
    await mongoDbService.createIndexes();
    console.log('✅ Created indexes successfully!');
    
    // Get health check
    const health = await mongoDbService.getHealthCheck();
    console.log('📊 MongoDB Health Check:', JSON.stringify(health, null, 2));
    
    // Insert test data
    const testCampaignInsight = {
      campaign_id: 'test_campaign_123',
      campaign_name: 'Test Campaign',
      account_id: 'act_123456789',
      date: new Date(),
      impressions: 1000,
      clicks: 50,
      spend: 100000,
      reach: 800,
      frequency: 1.25,
      ctr: 5.0,
      cpc: 2000,
      cpm: 100000,
      actions: [
        { action_type: 'purchase', value: 5 },
        { action_type: 'add_to_cart', value: 20 }
      ],
      created_at: new Date(),
      updated_at: new Date()
    };
    
    await mongoDbService.saveCampaignInsights([testCampaignInsight]);
    console.log('✅ Inserted test data successfully!');
    
    // Query test data
    const dateRange = {
      start: new Date(new Date().setDate(new Date().getDate() - 7)),
      end: new Date()
    };
    
    const insights = await mongoDbService.getCampaignInsights('act_123456789', dateRange);
    console.log(`📊 Found ${insights.length} campaign insights`);
    
    if (insights.length > 0) {
      console.log('📊 Sample insight:', JSON.stringify(insights[0], null, 2));
    }
    
    // Get performance summary
    const summary = await mongoDbService.getPerformanceSummary(
      'act_123456789',
      'campaign',
      'test_campaign_123',
      dateRange
    );
    
    console.log('📊 Performance Summary:', JSON.stringify(summary, null, 2));
    
    // Disconnect
    await mongoDbService.disconnect();
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ MongoDB test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testMongoDBConnection();
