import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ointer, DollarSign, Users } from 'lucide-react';
import { Insights } from '../types/facebook';

interface MetricsCardsProps {
  insights: Insights[];
}

export const MetricsCards: React.FC<MetricsCardsProps> = ({ insights }) => {
  // Calculate totals from insights data
  const totals = insights.reduce((acc, insight) => {
    acc.impressions += parseInt(insight.impressions) || 0;
    acc.clicks += parseInt(insight.clicks) || 0;
    acc.spend += parseFloat(insight.spend) || 0;
    acc.reach += parseInt(insight.reach) || 0;
    return acc;
  }, { impressions: 0, clicks: 0, spend: 0, reach: 0 });

  const ctr = totals.impressions > 0 ? ((totals.clicks / totals.impressions) * 100).toFixed(2) : '0.00';
  const cpc = totals.clicks > 0 ? (totals.spend / totals.clicks).toFixed(2) : '0.00';

  const metrics = [
    {
      title: 'Tổng chi phí',
      value: `${totals.spend.toLocaleString('vi-VN')} ₫`,
      icon: DollarSign,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700',
      change: '+12.5%',
      changeType: 'increase'
    },
    {
      title: 'Lượt hiển thị',
      value: totals.impressions.toLocaleString(),
      icon: Eye,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700',
      change: '+8.3%',
      changeType: 'increase'
    },
    {
      title: 'Lượt nhấp',
      value: totals.clicks.toLocaleString(),
      icon: MousePointer,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700',
      change: '+15.7%',
      changeType: 'increase'
    },
    {
      title: 'Tiếp cận',
      value: totals.reach.toLocaleString(),
      icon: Users,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-700',
      change: '+5.2%',
      changeType: 'increase'
    },
    {
      title: 'CTR',
      value: `${ctr}%`,
      icon: TrendingUp,
      color: 'bg-indigo-500',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-700',
      change: '+2.1%',
      changeType: 'increase'
    },
    {
      title: 'CPC',
      value: `${parseFloat(cpc).toLocaleString('vi-VN')} ₫`,
      icon: DollarSign,
      color: 'bg-teal-500',
      bgColor: 'bg-teal-50',
      textColor: 'text-teal-700',
      change: '-3.4%',
      changeType: 'decrease'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
      {metrics.map((metric, index) => (
        <div key={index} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-6 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
          <div className="flex items-center justify-between mb-4">
            <div>
              <p className="text-sm font-semibold text-gray-600 mb-2">{metric.title}</p>
              <p className="text-2xl font-bold text-gray-900 mb-1">{metric.value}</p>
              <div className={`flex items-center space-x-1 text-xs font-medium ${
                metric.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
              }`}>
                <TrendingUp className={`w-3 h-3 ${
                  metric.changeType === 'decrease' ? 'rotate-180' : ''
                }`} />
                <span>{metric.change}</span>
              </div>
            </div>
            <div className={`p-4 rounded-2xl ${metric.bgColor} shadow-sm`}>
              <metric.icon className={`w-6 h-6 ${metric.textColor}`} />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};