import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Users, 
  CreditCard, 
  Globe, 
  Eye, 
  Settings,
  Plus,
  Share2,
  Shield,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Search,
  Filter
} from 'lucide-react';
import { businessManagerApi } from '../utils/businessManagerApi';
import { CreateAdAccountModal } from './CreateAdAccountModal';
import { AssetSharingModal } from './AssetSharingModal';
import {
  BusinessManager,
  BusinessUser,
  ExtendedAdAccount,
  Page,
  Pixel
} from '../types/facebook';

interface BusinessManagerDashboardProps {
  accessToken: string;
  isMockMode?: boolean;
}

export const BusinessManagerDashboard: React.FC<BusinessManagerDashboardProps> = ({ 
  accessToken, 
  isMockMode = false 
}) => {
  const [businessManagers, setBusinessManagers] = useState<BusinessManager[]>([]);
  const [selectedBM, setSelectedBM] = useState<BusinessManager | null>(null);
  const [bmDetails, setBmDetails] = useState<BusinessManager | null>(null);
  const [businessUsers, setBusinessUsers] = useState<BusinessUser[]>([]);
  const [adAccounts, setAdAccounts] = useState<ExtendedAdAccount[]>([]);
  const [pages, setPages] = useState<Page[]>([]);
  const [pixels, setPixels] = useState<Pixel[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'accounts' | 'pages' | 'pixels' | 'users' | 'settings'>('overview');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateAccountModal, setShowCreateAccountModal] = useState(false);
  const [showSharingModal, setShowSharingModal] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<{
    id: string;
    type: 'AD_ACCOUNT' | 'PAGE' | 'PIXEL' | 'APP';
    name: string;
  } | null>(null);

  useEffect(() => {
    businessManagerApi.setAccessToken(accessToken);
    businessManagerApi.setMockMode(isMockMode);
    loadBusinessManagers();
  }, [accessToken, isMockMode]);

  useEffect(() => {
    if (selectedBM) {
      loadBusinessManagerData();
    }
  }, [selectedBM]);

  const loadBusinessManagers = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const bms = await businessManagerApi.getBusinessManagers();
      setBusinessManagers(bms);
      if (bms.length > 0) {
        setSelectedBM(bms[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load business managers');
    } finally {
      setIsLoading(false);
    }
  };

  const loadBusinessManagerData = async () => {
    if (!selectedBM) return;

    setIsLoading(true);
    try {
      const [details, users, accounts, bmPages, bmPixels] = await Promise.all([
        businessManagerApi.getBusinessManagerDetails(selectedBM.id),
        businessManagerApi.getBusinessUsers(selectedBM.id),
        businessManagerApi.getBusinessAdAccounts(selectedBM.id),
        businessManagerApi.getBusinessPages(selectedBM.id),
        businessManagerApi.getBusinessPixels(selectedBM.id)
      ]);

      setBmDetails(details);
      setBusinessUsers(users);
      setAdAccounts(accounts);
      setPages(bmPages);
      setPixels(bmPixels);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load business manager data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateAccountSuccess = (newAccount: ExtendedAdAccount) => {
    setAdAccounts(prev => [...prev, newAccount]);
    setShowCreateAccountModal(false);
  };

  const handleShareAsset = (assetId: string, assetType: 'AD_ACCOUNT' | 'PAGE' | 'PIXEL' | 'APP', assetName: string) => {
    setSelectedAsset({ id: assetId, type: assetType, name: assetName });
    setShowSharingModal(true);
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-red-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-red-100 text-red-800';
    }
  };

  if (isLoading && !selectedBM) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Đang tải Business Managers...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex items-center">
          <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
          <span className="text-red-800 font-medium">Lỗi: {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Building2 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Business Manager Dashboard</h1>
              <p className="text-gray-600">Quản lý tất cả Business Managers và tài sản</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2">
              <Plus className="w-4 h-4" />
              <span>Tạo BM mới</span>
            </button>
          </div>
        </div>

        {/* BM Selector */}
        <div className="flex items-center space-x-4">
          <select
            value={selectedBM?.id || ''}
            onChange={(e) => {
              const bm = businessManagers.find(b => b.id === e.target.value);
              setSelectedBM(bm || null);
            }}
            className="px-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white min-w-[300px]"
          >
            {businessManagers.map(bm => (
              <option key={bm.id} value={bm.id}>
                {bm.name} ({bm.id})
              </option>
            ))}
          </select>

          {selectedBM && (
            <div className="flex items-center space-x-2">
              {getVerificationIcon(selectedBM.verification_status)}
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedBM.verification_status)}`}>
                {selectedBM.verification_status}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Tổng quan', icon: TrendingUp },
              { id: 'accounts', label: 'Tài khoản QC', icon: CreditCard },
              { id: 'pages', label: 'Fanpages', icon: Globe },
              { id: 'pixels', label: 'Pixels', icon: Eye },
              { id: 'users', label: 'Người dùng', icon: Users },
              { id: 'settings', label: 'Cài đặt', icon: Settings }
            ].map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-2">
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'overview' && bmDetails && (
            <div className="space-y-6">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-600 text-sm font-medium">Tài khoản QC</p>
                      <p className="text-2xl font-bold text-blue-900">{adAccounts.length}</p>
                    </div>
                    <CreditCard className="w-8 h-8 text-blue-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-600 text-sm font-medium">Fanpages</p>
                      <p className="text-2xl font-bold text-green-900">{pages.length}</p>
                    </div>
                    <Globe className="w-8 h-8 text-green-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-600 text-sm font-medium">Pixels</p>
                      <p className="text-2xl font-bold text-purple-900">{pixels.length}</p>
                    </div>
                    <Eye className="w-8 h-8 text-purple-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-600 text-sm font-medium">Người dùng</p>
                      <p className="text-2xl font-bold text-orange-900">{businessUsers.length}</p>
                    </div>
                    <Users className="w-8 h-8 text-orange-600" />
                  </div>
                </div>
              </div>

              {/* BM Info */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin Business Manager</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Tên</p>
                    <p className="font-medium text-gray-900">{bmDetails.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">ID</p>
                    <p className="font-medium text-gray-900 font-mono">{bmDetails.id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Ngày tạo</p>
                    <p className="font-medium text-gray-900">
                      {new Date(bmDetails.created_time).toLocaleDateString('vi-VN')}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Trạng thái xác minh</p>
                    <div className="flex items-center space-x-2">
                      {getVerificationIcon(bmDetails.verification_status)}
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(bmDetails.verification_status)}`}>
                        {bmDetails.verification_status}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'accounts' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Tài khoản quảng cáo ({adAccounts.length})</h3>
                <button
                  onClick={() => setShowCreateAccountModal(true)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Tạo TKQC mới</span>
                </button>
              </div>

              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tên tài khoản
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tiền tệ
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Đã chi tiêu
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Số dư
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hành động
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {adAccounts.map((account) => (
                      <tr key={account.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{account.name}</div>
                            <div className="text-sm text-gray-500 font-mono">{account.id}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {account.currency}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {account.amount_spent ? `${parseInt(account.amount_spent).toLocaleString()} ${account.currency}` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {account.balance ? `${parseInt(account.balance).toLocaleString()} ${account.currency}` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            account.account_status === 1 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {account.account_status === 1 ? 'Hoạt động' : 'Tạm dừng'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleShareAsset(account.id, 'AD_ACCOUNT', account.name)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                            title="Chia sẻ tài khoản"
                          >
                            <Share2 className="w-4 h-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-900" title="Cài đặt">
                            <Settings className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Other tabs content will be added here */}
          {activeTab !== 'overview' && activeTab !== 'accounts' && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Settings className="w-12 h-12 mx-auto" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === 'pages' && 'Quản lý Fanpages'}
                {activeTab === 'pixels' && 'Quản lý Pixels'}
                {activeTab === 'users' && 'Quản lý người dùng'}
                {activeTab === 'settings' && 'Cài đặt Business Manager'}
              </h3>
              <p className="text-gray-600">Tính năng đang được phát triển...</p>
            </div>
          )}
        </div>
      </div>

      {/* Create Ad Account Modal */}
      {selectedBM && (
        <CreateAdAccountModal
          isOpen={showCreateAccountModal}
          onClose={() => setShowCreateAccountModal(false)}
          businessId={selectedBM.id}
          onSuccess={handleCreateAccountSuccess}
        />
      )}

      {/* Asset Sharing Modal */}
      {selectedBM && selectedAsset && (
        <AssetSharingModal
          isOpen={showSharingModal}
          onClose={() => {
            setShowSharingModal(false);
            setSelectedAsset(null);
          }}
          assetId={selectedAsset.id}
          assetType={selectedAsset.type}
          assetName={selectedAsset.name}
          currentBusinessId={selectedBM.id}
        />
      )}
    </div>
  );
};
