import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  Target,
  DollarSign,
  Eye,
  MousePointer,
  BarChart3,
  Lightbulb,
  Award,
  AlertCircle
} from 'lucide-react';
import { AnalyticsEngine } from '../utils/analyticsEngine';
import { mockAdPerformanceData } from '../utils/mockAnalyticsData';
import { AdPerformanceMetrics, AdAnalysisResult, ComparisonReport } from '../types/analytics';

export const PerformanceAnalyzer: React.FC = () => {
  const [analysisResults, setAnalysisResults] = useState<AdAnalysisResult[]>([]);
  const [comparisonReport, setComparisonReport] = useState<ComparisonReport | null>(null);
  const [selectedAd, setSelectedAd] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading and analysis
    setIsLoading(true);
    setTimeout(() => {
      const results = mockAdPerformanceData.map(ad => 
        AnalyticsEngine.analyzeAdPerformance(ad)
      );
      setAnalysisResults(results);
      setComparisonReport(AnalyticsEngine.generateComparisonReport(mockAdPerformanceData));
      setIsLoading(false);
    }, 1500);
  }, []);

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'EXCELLENT': return 'text-green-600 bg-green-100';
      case 'GOOD': return 'text-blue-600 bg-blue-100';
      case 'AVERAGE': return 'text-yellow-600 bg-yellow-100';
      case 'POOR': return 'text-orange-600 bg-orange-100';
      case 'CRITICAL': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'EXCELLENT': return <Award className="w-5 h-5" />;
      case 'GOOD': return <CheckCircle className="w-5 h-5" />;
      case 'AVERAGE': return <Target className="w-5 h-5" />;
      case 'POOR': return <AlertTriangle className="w-5 h-5" />;
      case 'CRITICAL': return <AlertCircle className="w-5 h-5" />;
      default: return <BarChart3 className="w-5 h-5" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH': return 'text-red-700 bg-red-100 border-red-200';
      case 'MEDIUM': return 'text-yellow-700 bg-yellow-100 border-yellow-200';
      case 'LOW': return 'text-green-700 bg-green-100 border-green-200';
      default: return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-16">
        <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
          <BarChart3 className="w-8 h-8 animate-pulse text-white" />
        </div>
        <span className="text-lg font-medium text-gray-700">Đang phân tích hiệu suất quảng cáo...</span>
        <span className="text-sm text-gray-500 mt-1">Vui lòng chờ trong giây lát</span>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="flex items-center space-x-4 mb-4">
          <BarChart3 className="w-8 h-8" />
          <h2 className="text-3xl font-bold">Phân Tích Hiệu Suất Quảng Cáo</h2>
        </div>
        <p className="text-blue-100 text-lg">
          Phân tích chi tiết từng quảng cáo với đề xuất tối ưu hóa chuyên nghiệp
        </p>
      </div>

      {/* Summary Report */}
      {comparisonReport && (
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
            <TrendingUp className="w-6 h-6 mr-3 text-green-600" />
            Tổng Quan Hiệu Suất
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-green-700 mb-1">Tổng Chi Phí</p>
                  <p className="text-2xl font-bold text-green-900">
                    {comparisonReport.totalSpend.toLocaleString('vi-VN')} ₫
                  </p>
                </div>
                <DollarSign className="w-8 h-8 text-green-600" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl p-6 border border-blue-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-blue-700 mb-1">Tổng Conversions</p>
                  <p className="text-2xl font-bold text-blue-900">
                    {comparisonReport.totalConversions.toLocaleString()}
                  </p>
                </div>
                <Target className="w-8 h-8 text-blue-600" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-purple-700 mb-1">ROAS Trung Bình</p>
                  <p className="text-2xl font-bold text-purple-900">
                    {comparisonReport.overallROAS.toFixed(2)}
                  </p>
                </div>
                <TrendingUp className="w-8 h-8 text-purple-600" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-xl p-6 border border-orange-100">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-semibold text-orange-700 mb-1">CTR Trung Bình</p>
                  <p className="text-2xl font-bold text-orange-900">
                    {comparisonReport.averageMetrics.ctr?.toFixed(2)}%
                  </p>
                </div>
                <MousePointer className="w-8 h-8 text-orange-600" />
              </div>
            </div>
          </div>

          {/* Top & Under Performers */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2 text-green-600" />
                Top Performers
              </h4>
              <div className="space-y-3">
                {comparisonReport.topPerformers.map((ad, index) => (
                  <div key={ad.adId} className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-green-900">{ad.adName}</p>
                        <p className="text-sm text-green-700">ROAS: {ad.roas.toFixed(2)} | CTR: {ad.ctr.toFixed(2)}%</p>
                      </div>
                      <div className="text-2xl font-bold text-green-600">#{index + 1}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2 text-red-600" />
                Cần Cải Thiện
              </h4>
              <div className="space-y-3">
                {comparisonReport.underPerformers.map((ad, index) => (
                  <div key={ad.adId} className="bg-red-50 rounded-lg p-4 border border-red-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-semibold text-red-900">{ad.adName}</p>
                        <p className="text-sm text-red-700">ROAS: {ad.roas.toFixed(2)} | CTR: {ad.ctr.toFixed(2)}%</p>
                      </div>
                      <AlertCircle className="w-6 h-6 text-red-600" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Individual Ad Analysis */}
      <div className="space-y-6">
        <h3 className="text-2xl font-bold text-gray-900 flex items-center">
          <Target className="w-6 h-6 mr-3 text-blue-600" />
          Phân Tích Chi Tiết Từng Quảng Cáo
        </h3>

        {analysisResults.map((result) => (
          <div key={result.ad.adId} className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 overflow-hidden">
            {/* Ad Header */}
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-200/50">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="text-xl font-bold text-gray-900 mb-2">{result.ad.adName}</h4>
                  <p className="text-gray-600">{result.ad.campaignName} → {result.ad.adSetName}</p>
                </div>
                <div className={`flex items-center space-x-2 px-4 py-2 rounded-full ${getPerformanceColor(result.performance)}`}>
                  {getPerformanceIcon(result.performance)}
                  <span className="font-bold">{result.performance}</span>
                </div>
              </div>
            </div>

            <div className="p-6">
              {/* KPI Metrics */}
              <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-4 mb-8">
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">Chi phí</p>
                  <p className="text-lg font-bold text-gray-900">{result.ad.totalSpend.toLocaleString('vi-VN')} ₫</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">CPC</p>
                  <p className="text-lg font-bold text-gray-900">{result.ad.cpc.toLocaleString('vi-VN')} ₫</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">CPM</p>
                  <p className="text-lg font-bold text-gray-900">{result.ad.cpm.toLocaleString('vi-VN')} ₫</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">CTR</p>
                  <p className="text-lg font-bold text-blue-600">{result.ad.ctr.toFixed(2)}%</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">CVR</p>
                  <p className="text-lg font-bold text-green-600">{result.ad.cvr.toFixed(2)}%</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">ROAS</p>
                  <p className="text-lg font-bold text-purple-600">{result.ad.roas.toFixed(2)}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">Conversions</p>
                  <p className="text-lg font-bold text-gray-900">{result.ad.conversions}</p>
                </div>
                <div className="text-center">
                  <p className="text-xs font-semibold text-gray-500 mb-1">Quality Score</p>
                  <p className="text-lg font-bold text-indigo-600">{result.ad.qualityScore}/10</p>
                </div>
              </div>

              {/* Strengths & Weaknesses */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div>
                  <h5 className="text-lg font-bold text-green-700 mb-4 flex items-center">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Điểm Mạnh
                  </h5>
                  <div className="space-y-2">
                    {result.strengths.map((strength, index) => (
                      <div key={index} className="bg-green-50 rounded-lg p-3 border border-green-200">
                        <p className="text-sm text-green-800">{strength}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h5 className="text-lg font-bold text-red-700 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2" />
                    Điểm Yếu
                  </h5>
                  <div className="space-y-2">
                    {result.weaknesses.length > 0 ? result.weaknesses.map((weakness, index) => (
                      <div key={index} className="bg-red-50 rounded-lg p-3 border border-red-200">
                        <p className="text-sm text-red-800">{weakness}</p>
                      </div>
                    )) : (
                      <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                        <p className="text-sm text-gray-600">Không có điểm yếu đáng kể</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Budget Optimization */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200 mb-6">
                <h5 className="text-lg font-bold text-blue-900 mb-4 flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Tối Ưu Hóa Ngân Sách
                </h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-sm font-semibold text-blue-700 mb-1">Ngân sách hiện tại</p>
                    <p className="text-xl font-bold text-blue-900">
                      {result.budgetOptimization.currentBudget.toLocaleString('vi-VN')} ₫
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-semibold text-blue-700 mb-1">Ngân sách đề xuất</p>
                    <p className="text-xl font-bold text-green-600">
                      {result.budgetOptimization.recommendedBudget.toLocaleString('vi-VN')} ₫
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-semibold text-blue-700 mb-1">Thay đổi</p>
                    <p className={`text-xl font-bold ${
                      result.budgetOptimization.recommendedBudget > result.budgetOptimization.currentBudget 
                        ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {result.budgetOptimization.recommendedBudget > result.budgetOptimization.currentBudget ? '+' : ''}
                      {((result.budgetOptimization.recommendedBudget - result.budgetOptimization.currentBudget) / result.budgetOptimization.currentBudget * 100).toFixed(0)}%
                    </p>
                  </div>
                </div>
                <p className="text-blue-800 bg-blue-100 rounded-lg p-3">
                  <strong>Lý do:</strong> {result.budgetOptimization.reasoning}
                </p>
              </div>

              {/* Recommendations */}
              <div>
                <h5 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
                  Đề Xuất Cải Thiện
                </h5>
                <div className="space-y-4">
                  {result.recommendations.map((rec, index) => (
                    <div key={index} className={`rounded-xl p-4 border ${getPriorityColor(rec.priority)}`}>
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <div className="flex items-center space-x-2 mb-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-bold ${getPriorityColor(rec.priority)}`}>
                              {rec.priority}
                            </span>
                            <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs font-medium">
                              {rec.category}
                            </span>
                          </div>
                          <h6 className="font-bold text-gray-900 mb-1">{rec.issue}</h6>
                        </div>
                      </div>
                      <p className="text-gray-800 mb-2"><strong>Đề xuất:</strong> {rec.recommendation}</p>
                      <p className="text-gray-700 mb-2"><strong>Tác động dự kiến:</strong> {rec.expectedImpact}</p>
                      {rec.estimatedBudgetChange !== 0 && (
                        <p className="text-gray-700">
                          <strong>Thay đổi ngân sách:</strong> 
                          <span className={rec.estimatedBudgetChange > 0 ? 'text-green-600' : 'text-red-600'}>
                            {rec.estimatedBudgetChange > 0 ? '+' : ''}{rec.estimatedBudgetChange.toLocaleString('vi-VN')} ₫
                          </span>
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};