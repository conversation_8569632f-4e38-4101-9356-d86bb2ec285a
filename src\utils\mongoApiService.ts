import { mongoDbService } from './mongoDbService';
import { Campaign, AdSet, Ad, Insights } from '../types/facebook';

class MongoApiService {
  private isConnected = false;

  async initialize(): Promise<void> {
    if (!this.isConnected) {
      await mongoDbService.connect();
      this.isConnected = true;
    }
  }

  // Get campaigns with latest insights
  async getCampaigns(accountId: string): Promise<Campaign[]> {
    await this.initialize();
    
    const dateRange = this.getDefaultDateRange();
    const insights = await mongoDbService.getCampaignInsights(accountId, dateRange);
    
    // Group insights by campaign and get latest data
    const campaignMap = new Map<string, Campaign>();
    
    insights.forEach(insight => {
      if (!campaignMap.has(insight.campaign_id)) {
        campaignMap.set(insight.campaign_id, {
          id: insight.campaign_id,
          name: insight.campaign_name,
          status: 'ACTIVE', // Default status, could be enhanced
          objective: 'CONVERSIONS', // Default objective
          created_time: insight.created_at.toISOString(),
          updated_time: insight.updated_at.toISOString()
        });
      }
    });
    
    return Array.from(campaignMap.values());
  }

  // Get ad sets with latest insights
  async getAdSets(accountId: string): Promise<AdSet[]> {
    await this.initialize();
    
    const dateRange = this.getDefaultDateRange();
    const insights = await mongoDbService.getAdSetInsights(accountId, dateRange);
    
    const adSetMap = new Map<string, AdSet>();
    
    insights.forEach(insight => {
      if (!adSetMap.has(insight.adset_id)) {
        adSetMap.set(insight.adset_id, {
          id: insight.adset_id,
          name: insight.adset_name,
          status: 'ACTIVE',
          campaign_id: insight.campaign_id,
          created_time: insight.created_at.toISOString(),
          updated_time: insight.updated_at.toISOString()
        });
      }
    });
    
    return Array.from(adSetMap.values());
  }

  // Get ads with latest insights
  async getAds(accountId: string): Promise<Ad[]> {
    await this.initialize();
    
    const dateRange = this.getDefaultDateRange();
    const insights = await mongoDbService.getAdInsights(accountId, dateRange);
    
    const adMap = new Map<string, Ad>();
    
    insights.forEach(insight => {
      if (!adMap.has(insight.ad_id)) {
        adMap.set(insight.ad_id, {
          id: insight.ad_id,
          name: insight.ad_name,
          status: 'ACTIVE',
          adset_id: insight.adset_id,
          campaign_id: insight.campaign_id,
          creative: insight.creative,
          created_time: insight.created_at.toISOString(),
          updated_time: insight.updated_at.toISOString()
        });
      }
    });
    
    return Array.from(adMap.values());
  }

  // Get insights for specific item
  async getInsights(
    itemId: string, 
    level: 'campaign' | 'adset' | 'ad', 
    datePreset: string
  ): Promise<Insights[]> {
    await this.initialize();
    
    const dateRange = this.parseDatePreset(datePreset);
    let insights: any[] = [];
    
    switch (level) {
      case 'campaign':
        insights = await mongoDbService.getCampaignInsights('', dateRange, [itemId]);
        break;
      case 'adset':
        insights = await mongoDbService.getAdSetInsights('', dateRange, [itemId]);
        break;
      case 'ad':
        insights = await mongoDbService.getAdInsights('', dateRange, [itemId]);
        break;
    }
    
    // Convert MongoDB insights to Facebook API format
    return insights.map(insight => ({
      date_start: insight.date.toISOString().split('T')[0],
      date_stop: insight.date.toISOString().split('T')[0],
      impressions: insight.impressions.toString(),
      clicks: insight.clicks.toString(),
      spend: insight.spend.toString(),
      reach: insight.reach.toString(),
      frequency: insight.frequency.toString(),
      ctr: insight.ctr.toString(),
      cpc: insight.cpc.toString(),
      cpm: insight.cpm.toString(),
      actions: insight.actions || [],
      cost_per_action_type: insight.cost_per_action_type || []
    }));
  }

  // Get performance summary for an item
  async getPerformanceSummary(
    accountId: string,
    itemType: 'campaign' | 'adset' | 'ad',
    itemId: string,
    datePreset: string = 'last_7d'
  ): Promise<any> {
    await this.initialize();
    
    const dateRange = this.parseDatePreset(datePreset);
    const summary = await mongoDbService.getPerformanceSummary(
      accountId, 
      itemType, 
      itemId, 
      dateRange
    );
    
    if (!summary) {
      return null;
    }
    
    // Calculate derived metrics
    const ctr = summary.total_impressions > 0 
      ? (summary.total_clicks / summary.total_impressions) * 100 
      : 0;
    
    const cpc = summary.total_clicks > 0 
      ? summary.total_spend / summary.total_clicks 
      : 0;
    
    const cpm = summary.total_impressions > 0 
      ? (summary.total_spend / summary.total_impressions) * 1000 
      : 0;
    
    // Determine performance level
    let performance = 'poor';
    if (ctr >= 3.0 && cpc <= 8000 && cpm <= 40000) {
      performance = 'excellent';
    } else if (ctr >= 2.0 && cpc <= 12000 && cpm <= 60000) {
      performance = 'good';
    } else if (ctr >= 1.0 && cpc <= 20000 && cpm <= 80000) {
      performance = 'average';
    }
    
    return {
      impressions: summary.total_impressions,
      clicks: summary.total_clicks,
      spend: summary.total_spend,
      reach: summary.total_reach,
      frequency: summary.avg_frequency,
      ctr,
      cpc,
      cpm,
      performance,
      days_count: summary.days_count,
      daily_average: {
        impressions: Math.round(summary.total_impressions / summary.days_count),
        clicks: Math.round(summary.total_clicks / summary.days_count),
        spend: summary.total_spend / summary.days_count
      }
    };
  }

  // Get top performers
  async getTopPerformers(
    accountId: string,
    itemType: 'campaign' | 'adset' | 'ad',
    metric: 'ctr' | 'cpc' | 'spend' | 'impressions' = 'ctr',
    limit: number = 10,
    datePreset: string = 'last_7d'
  ): Promise<any[]> {
    await this.initialize();
    
    const dateRange = this.parseDatePreset(datePreset);
    const collectionName = `${itemType}_insights`;
    
    // This would require aggregation pipeline in MongoDB
    // For now, return empty array - can be implemented later
    return [];
  }

  // Get account overview
  async getAccountOverview(accountId: string, datePreset: string = 'last_7d'): Promise<any> {
    await this.initialize();
    
    const dateRange = this.parseDatePreset(datePreset);
    
    // Get aggregated data for all campaigns, adsets, and ads
    const [campaigns, adSets, ads] = await Promise.all([
      this.getCampaigns(accountId),
      this.getAdSets(accountId),
      this.getAds(accountId)
    ]);
    
    // Get total spend and impressions
    const campaignInsights = await mongoDbService.getCampaignInsights(accountId, dateRange);
    
    const totals = campaignInsights.reduce((acc, insight) => ({
      impressions: acc.impressions + insight.impressions,
      clicks: acc.clicks + insight.clicks,
      spend: acc.spend + insight.spend,
      reach: acc.reach + insight.reach
    }), { impressions: 0, clicks: 0, spend: 0, reach: 0 });
    
    const ctr = totals.impressions > 0 ? (totals.clicks / totals.impressions) * 100 : 0;
    const cpc = totals.clicks > 0 ? totals.spend / totals.clicks : 0;
    const cpm = totals.impressions > 0 ? (totals.spend / totals.impressions) * 1000 : 0;
    
    return {
      account_id: accountId,
      date_range: datePreset,
      summary: {
        campaigns_count: campaigns.length,
        adsets_count: adSets.length,
        ads_count: ads.length,
        total_impressions: totals.impressions,
        total_clicks: totals.clicks,
        total_spend: totals.spend,
        total_reach: totals.reach,
        average_ctr: ctr,
        average_cpc: cpc,
        average_cpm: cpm
      },
      last_updated: new Date()
    };
  }

  // Utility methods
  private getDefaultDateRange(): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 7); // Last 7 days
    
    return { start, end };
  }

  private parseDatePreset(preset: string): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();
    
    switch (preset) {
      case 'today':
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);
        break;
      case 'yesterday':
        start.setDate(start.getDate() - 1);
        start.setHours(0, 0, 0, 0);
        end.setDate(end.getDate() - 1);
        end.setHours(23, 59, 59, 999);
        break;
      case 'last_3d':
        start.setDate(start.getDate() - 3);
        break;
      case 'last_7d':
        start.setDate(start.getDate() - 7);
        break;
      case 'last_14d':
        start.setDate(start.getDate() - 14);
        break;
      case 'last_30d':
        start.setDate(start.getDate() - 30);
        break;
      default:
        start.setDate(start.getDate() - 7);
    }
    
    return { start, end };
  }

  // Health check
  async getHealthStatus(): Promise<any> {
    try {
      await this.initialize();
      const health = await mongoDbService.getHealthCheck();
      return {
        status: 'healthy',
        mongodb: health,
        api_service: 'connected'
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        api_service: 'disconnected'
      };
    }
  }

  // Manual data refresh
  async refreshData(accountId: string): Promise<void> {
    // This would trigger a manual sync
    // Implementation depends on how cron service is integrated
    console.log(`🔄 Manual refresh requested for account: ${accountId}`);
  }
}

export const mongoApiService = new MongoApiService();
