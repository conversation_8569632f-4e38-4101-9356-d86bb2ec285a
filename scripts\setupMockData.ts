import { mongoDbService } from '../src/utils/mongoDbService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Mock data generator
function generateMockInsights(itemId: string, itemName: string, itemType: 'campaign' | 'adset' | 'ad', days: number = 7) {
  const insights = [];
  const baseDate = new Date();
  
  for (let i = 0; i < days; i++) {
    const date = new Date(baseDate);
    date.setDate(date.getDate() - i);
    
    // Generate realistic metrics with some randomness
    const impressions = Math.floor(Math.random() * 10000) + 5000;
    const clicks = Math.floor(impressions * (Math.random() * 0.05 + 0.01)); // 1-6% CTR
    const spend = Math.floor(Math.random() * 1000000) + 500000; // 500k - 1.5M VND
    const reach = Math.floor(impressions * (Math.random() * 0.3 + 0.7)); // 70-100% of impressions
    
    const insight = {
      [`${itemType}_id`]: itemId,
      [`${itemType}_name`]: itemName,
      account_id: 'act_123456789',
      date: date,
      impressions,
      clicks,
      spend,
      reach,
      frequency: impressions / reach,
      ctr: (clicks / impressions) * 100,
      cpc: spend / clicks,
      cpm: (spend / impressions) * 1000,
      actions: [
        { action_type: 'purchase', value: Math.floor(clicks * 0.1) },
        { action_type: 'add_to_cart', value: Math.floor(clicks * 0.3) },
        { action_type: 'view_content', value: Math.floor(clicks * 0.8) }
      ],
      cost_per_action_type: [
        { action_type: 'purchase', value: spend / Math.max(1, Math.floor(clicks * 0.1)) },
        { action_type: 'add_to_cart', value: spend / Math.max(1, Math.floor(clicks * 0.3)) }
      ],
      created_at: new Date(),
      updated_at: new Date()
    };
    
    // Add campaign_id for adsets and ads
    if (itemType === 'adset') {
      insight.campaign_id = 'campaign_001';
    } else if (itemType === 'ad') {
      insight.campaign_id = 'campaign_001';
      insight.adset_id = 'adset_001';
      insight.creative = {
        title: 'Amazing Product Sale!',
        body: 'Get 50% off on all products. Limited time offer!',
        image_url: 'https://example.com/image.jpg'
      };
    }
    
    insights.push(insight);
  }
  
  return insights;
}

// Setup mock data
async function setupMockData() {
  console.log('🔍 Setting up mock data for testing...');
  
  try {
    // Connect to MongoDB
    await mongoDbService.connect();
    console.log('✅ Connected to MongoDB successfully!');
    
    // Create indexes
    await mongoDbService.createIndexes();
    console.log('✅ Created indexes successfully!');
    
    // Generate mock campaigns
    const campaigns = [
      { id: 'campaign_001', name: 'Black Friday 2024 Campaign' },
      { id: 'campaign_002', name: 'Christmas Sale Campaign' },
      { id: 'campaign_003', name: 'New Year Promotion' }
    ];
    
    console.log('📊 Generating campaign insights...');
    for (const campaign of campaigns) {
      const insights = generateMockInsights(campaign.id, campaign.name, 'campaign', 30);
      await mongoDbService.saveCampaignInsights(insights);
      console.log(`✅ Saved ${insights.length} insights for campaign: ${campaign.name}`);
    }
    
    // Generate mock ad sets
    const adSets = [
      { id: 'adset_001', name: 'Men 25-35 Interest Targeting' },
      { id: 'adset_002', name: 'Women 18-30 Lookalike Audience' },
      { id: 'adset_003', name: 'Retargeting Website Visitors' }
    ];
    
    console.log('📊 Generating adset insights...');
    for (const adSet of adSets) {
      const insights = generateMockInsights(adSet.id, adSet.name, 'adset', 30);
      await mongoDbService.saveAdSetInsights(insights);
      console.log(`✅ Saved ${insights.length} insights for adset: ${adSet.name}`);
    }
    
    // Generate mock ads
    const ads = [
      { id: 'ad_001', name: 'Video Ad - Product Demo' },
      { id: 'ad_002', name: 'Carousel Ad - Multiple Products' },
      { id: 'ad_003', name: 'Single Image Ad - Hero Product' },
      { id: 'ad_004', name: 'Collection Ad - Category Showcase' }
    ];
    
    console.log('📊 Generating ad insights...');
    for (const ad of ads) {
      const insights = generateMockInsights(ad.id, ad.name, 'ad', 30);
      await mongoDbService.saveAdInsights(insights);
      console.log(`✅ Saved ${insights.length} insights for ad: ${ad.name}`);
    }
    
    // Create sync logs
    console.log('📊 Creating sync logs...');
    const syncTypes = ['campaigns', 'adsets', 'ads'];
    for (const syncType of syncTypes) {
      const logId = await mongoDbService.logSyncStart('act_123456789', syncType as any);
      await mongoDbService.logSyncComplete(logId, 10); // 10 records synced
      console.log(`✅ Created sync log for: ${syncType}`);
    }
    
    // Get final health check
    const health = await mongoDbService.getHealthCheck();
    console.log('📊 Final MongoDB Health Check:', JSON.stringify(health, null, 2));
    
    // Disconnect
    await mongoDbService.disconnect();
    console.log('✅ Mock data setup completed successfully!');
    
    console.log('\n🎉 SETUP COMPLETE!');
    console.log('📊 Mock data has been created with:');
    console.log(`   - ${campaigns.length} campaigns with 30 days of insights each`);
    console.log(`   - ${adSets.length} ad sets with 30 days of insights each`);
    console.log(`   - ${ads.length} ads with 30 days of insights each`);
    console.log(`   - Sync logs for all types`);
    console.log('\n🚀 You can now test the dashboard with this data!');
    
  } catch (error) {
    console.error('❌ Mock data setup failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the setup
setupMockData();
