import React from 'react';
import { PerformanceSummaryCard } from './PerformanceSummaryCard';
import { DetailedInsightsModal } from './DetailedInsightsModal';
import { EnhancedInsightsView } from './EnhancedInsightsView';

// Mock data for demo
const mockCampaigns = [
  {
    id: 'campaign_1',
    name: '<PERSON><PERSON><PERSON> dịch Black Friday 2024',
    status: 'ACTIVE',
    objective: 'CONVERSIONS',
    created_time: '2024-01-15T10:30:00Z',
    updated_time: '2024-01-20T15:45:00Z'
  },
  {
    id: 'campaign_2', 
    name: '<PERSON><PERSON><PERSON> dị<PERSON>ế<PERSON>',
    status: 'ACTIVE',
    objective: 'TRAFFIC',
    created_time: '2024-01-10T09:20:00Z',
    updated_time: '2024-01-18T11:30:00Z'
  }
];

const mockAdSets = [
  {
    id: 'adset_1',
    name: 'Nhóm QC Nam 25-35',
    status: 'ACTIVE',
    campaign_id: 'campaign_1',
    created_time: '2024-01-15T11:00:00Z',
    updated_time: '2024-01-20T16:00:00Z'
  },
  {
    id: 'adset_2',
    name: '<PERSON><PERSON><PERSON>m QC Nữ 18-30',
    status: 'ACTIVE', 
    campaign_id: 'campaign_1',
    created_time: '2024-01-15T11:30:00Z',
    updated_time: '2024-01-20T16:30:00Z'
  }
];

const mockAds = [
  {
    id: 'ad_1',
    name: 'QC Video Black Friday',
    status: 'ACTIVE',
    adset_id: 'adset_1',
    creative: {
      id: 'creative_1',
      title: 'Giảm giá 50% Black Friday',
      body: 'Khuyến mãi lớn nhất năm!',
      image_url: 'https://example.com/image1.jpg'
    },
    created_time: '2024-01-15T12:00:00Z',
    updated_time: '2024-01-20T17:00:00Z'
  },
  {
    id: 'ad_2',
    name: 'QC Hình ảnh Tết',
    status: 'ACTIVE',
    adset_id: 'adset_2', 
    creative: {
      id: 'creative_2',
      title: 'Chúc Tết An Khang',
      body: 'Sản phẩm chất lượng cho Tết',
      image_url: 'https://example.com/image2.jpg'
    },
    created_time: '2024-01-15T12:30:00Z',
    updated_time: '2024-01-20T17:30:00Z'
  }
];

export const PerformanceDemo: React.FC = () => {
  const [showModal, setShowModal] = React.useState(false);
  const [selectedItem, setSelectedItem] = React.useState<{id: string, name: string, type: string} | null>(null);

  const handleShowDetails = (id: string, name: string, type: string) => {
    setSelectedItem({ id, name, type });
    setShowModal(true);
  };

  return (
    <div className="p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          🎯 Demo Chi Tiết Hiệu Suất
        </h1>
        <p className="text-gray-600">
          Trải nghiệm tính năng phân tích hiệu suất từng quảng cáo và chiến dịch
        </p>
      </div>

      {/* Performance Summary Cards Demo */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">
          📊 Performance Summary Cards
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {mockCampaigns.map(campaign => (
            <div key={campaign.id} className="relative group">
              <PerformanceSummaryCard
                itemId={campaign.id}
                itemName={campaign.name}
                itemType="campaign"
                accountId="act_123456789"
                className="cursor-pointer"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <button
                  onClick={() => handleShowDetails(campaign.id, campaign.name, 'campaign')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Xem chi tiết
                </button>
              </div>
            </div>
          ))}
          
          {mockAdSets.map(adset => (
            <div key={adset.id} className="relative group">
              <PerformanceSummaryCard
                itemId={adset.id}
                itemName={adset.name}
                itemType="adset"
                accountId="act_123456789"
                className="cursor-pointer"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <button
                  onClick={() => handleShowDetails(adset.id, adset.name, 'adset')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Xem chi tiết
                </button>
              </div>
            </div>
          ))}

          {mockAds.map(ad => (
            <div key={ad.id} className="relative group">
              <PerformanceSummaryCard
                itemId={ad.id}
                itemName={ad.name}
                itemType="ad"
                accountId="act_123456789"
                className="cursor-pointer"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <button
                  onClick={() => handleShowDetails(ad.id, ad.name, 'ad')}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-lg"
                >
                  Xem chi tiết
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Enhanced Insights View Demo */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">
          📈 Enhanced Insights View
        </h2>
        <div className="bg-gray-50 rounded-xl p-6">
          <EnhancedInsightsView
            accountId="act_123456789"
            campaigns={mockCampaigns}
            adSets={mockAdSets}
            ads={mockAds}
          />
        </div>
      </div>

      {/* Features List */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          ✨ Tính năng đã triển khai
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900">🎯 Performance Analysis</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Performance scoring (Xuất sắc → Cần cải thiện)</li>
              <li>• Key metrics: CTR, CPC, CPM, ROAS</li>
              <li>• Real-time data loading</li>
              <li>• Currency formatting (VND)</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900">🔍 Interactive Features</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Search & filter functionality</li>
              <li>• Grid và List view modes</li>
              <li>• Detailed insights modal</li>
              <li>• Responsive design</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900">🎨 UI/UX Improvements</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Color-coded performance indicators</li>
              <li>• Smooth hover effects</li>
              <li>• Loading states với skeleton UI</li>
              <li>• Modern gradient backgrounds</li>
            </ul>
          </div>
          
          <div className="space-y-2">
            <h3 className="font-medium text-gray-900">⚡ Performance Optimizations</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• React.memo cho components</li>
              <li>• API request caching</li>
              <li>• Debounced search input</li>
              <li>• Lazy loading modals</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Detailed Insights Modal */}
      {selectedItem && (
        <DetailedInsightsModal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setSelectedItem(null);
          }}
          itemId={selectedItem.id}
          itemName={selectedItem.name}
          itemType={selectedItem.type as any}
          accountId="act_123456789"
        />
      )}
    </div>
  );
};
