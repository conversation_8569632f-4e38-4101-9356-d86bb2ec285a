import { 
  BusinessManager, 
  BusinessUser, 
  Page, 
  Pixel, 
  <PERSON><PERSON>, 
  AssetSharingRequest,
  ExtendedAdAccount,
  FacebookApiResponse 
} from '../types/facebook';

declare global {
  interface Window {
    FB: any;
  }
}

class BusinessManagerApiService {
  private accessToken: string | null = null;
  private useMockData = false;

  setAccessToken(token: string): void {
    this.accessToken = token;
  }

  setMockMode(enabled: boolean): void {
    this.useMockData = enabled;
  }

  // Get all Business Managers user has access to
  async getBusinessManagers(): Promise<BusinessManager[]> {
    if (this.useMockData) {
      return this.getMockBusinessManagers();
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api('/me/businesses', {
        fields: 'id,name,primary_page,created_time,updated_time,verification_status,is_hidden,link,timezone_id,two_factor_type,user_access_expire_time,created_by'
      }, (response: FacebookApiResponse<BusinessManager>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business managers'));
        }
      });
    });
  }

  // Get Business Manager details
  async getBusinessManagerDetails(businessId: string): Promise<BusinessManager> {
    if (this.useMockData) {
      return this.getMockBusinessManagerDetails(businessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}`, {
        fields: 'id,name,primary_page,created_time,updated_time,verification_status,is_hidden,link,timezone_id,two_factor_type,user_access_expire_time,created_by,owned_ad_accounts{id,name,account_id,currency,account_status},owned_pages{id,name,category,fan_count,link,picture},owned_pixels{id,name,creation_time,code},owned_apps{id,name,category,link}'
      }, (response: BusinessManager) => {
        if (response && !response.error) {
          resolve(response);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business manager details'));
        }
      });
    });
  }

  // Get Business Users
  async getBusinessUsers(businessId: string): Promise<BusinessUser[]> {
    if (this.useMockData) {
      return this.getMockBusinessUsers(businessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}/business_users`, {
        fields: 'id,name,email,role,pending,status,invited_by,invite_link,permissions'
      }, (response: FacebookApiResponse<BusinessUser>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business users'));
        }
      });
    });
  }

  // Get Ad Accounts owned by Business Manager
  async getBusinessAdAccounts(businessId: string): Promise<ExtendedAdAccount[]> {
    if (this.useMockData) {
      return this.getMockBusinessAdAccounts(businessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}/owned_ad_accounts`, {
        fields: 'id,name,account_id,currency,account_status,business,access_type,permitted_roles,tasks,tos_accepted,funding_source,funding_source_details,is_notifications_enabled,spend_cap,amount_spent,balance,disable_reason'
      }, (response: FacebookApiResponse<ExtendedAdAccount>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business ad accounts'));
        }
      });
    });
  }

  // Get Pages owned by Business Manager
  async getBusinessPages(businessId: string): Promise<Page[]> {
    if (this.useMockData) {
      return this.getMockBusinessPages(businessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}/owned_pages`, {
        fields: 'id,name,category,category_list,tasks,access_token,can_post,fan_count,link,picture,verification_status,is_published'
      }, (response: FacebookApiResponse<Page>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business pages'));
        }
      });
    });
  }

  // Get Pixels owned by Business Manager
  async getBusinessPixels(businessId: string): Promise<Pixel[]> {
    if (this.useMockData) {
      return this.getMockBusinessPixels(businessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}/owned_pixels`, {
        fields: 'id,name,creation_time,last_fired_time,code,is_created_by_business,owner_business,owner_ad_account,is_unavailable,data_use_setting,enable_automatic_matching,first_party_cookie_status,is_restricted_use,matched_domain_uri'
      }, (response: FacebookApiResponse<Pixel>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch business pixels'));
        }
      });
    });
  }

  // Create new Ad Account in Business Manager
  async createAdAccount(businessId: string, accountData: {
    name: string;
    currency: string;
    timezone_id: number;
    end_advertiser?: string;
    media_agency?: string;
    partner?: string;
  }): Promise<ExtendedAdAccount> {
    if (this.useMockData) {
      return this.getMockCreateAdAccount(businessId, accountData);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${businessId}/adaccount`, 'POST', accountData, (response: ExtendedAdAccount) => {
        if (response && !response.error) {
          resolve(response);
        } else {
          reject(new Error(response.error?.message || 'Failed to create ad account'));
        }
      });
    });
  }

  // Share Asset (Ad Account, Page, Pixel, App)
  async shareAsset(businessId: string, assetId: string, assetType: 'AD_ACCOUNT' | 'PAGE' | 'PIXEL' | 'APP', targetBusinessId: string): Promise<AssetSharingRequest> {
    if (this.useMockData) {
      return this.getMockShareAsset(businessId, assetId, assetType, targetBusinessId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const endpoint = `/${businessId}/asset_sharing_requests`;
    const data = {
      asset_id: assetId,
      asset_type: assetType,
      business: targetBusinessId
    };

    return new Promise((resolve, reject) => {
      window.FB.api(endpoint, 'POST', data, (response: AssetSharingRequest) => {
        if (response && !response.error) {
          resolve(response);
        } else {
          reject(new Error(response.error?.message || 'Failed to share asset'));
        }
      });
    });
  }

  // Mock Data Methods
  private getMockBusinessManagers(): Promise<BusinessManager[]> {
    return Promise.resolve([
      {
        id: 'business_123',
        name: 'Công ty ABC Business Manager',
        created_time: '2023-01-15T10:30:00Z',
        updated_time: '2024-01-15T10:30:00Z',
        verification_status: 'verified',
        is_hidden: false,
        link: 'https://business.facebook.com/business_123',
        timezone_id: 7,
        two_factor_type: 'none',
        primary_page: {
          id: 'page_123',
          name: 'Công ty ABC Official Page'
        }
      },
      {
        id: 'business_456',
        name: 'Cửa hàng XYZ Business Manager',
        created_time: '2023-06-20T14:20:00Z',
        updated_time: '2024-01-10T09:15:00Z',
        verification_status: 'verified',
        is_hidden: false,
        link: 'https://business.facebook.com/business_456',
        timezone_id: 7,
        two_factor_type: 'none',
        primary_page: {
          id: 'page_456',
          name: 'Cửa hàng XYZ Fanpage'
        }
      }
    ]);
  }

  private getMockBusinessManagerDetails(businessId: string): Promise<BusinessManager> {
    const mockBM: BusinessManager = {
      id: businessId,
      name: businessId === 'business_123' ? 'Công ty ABC Business Manager' : 'Cửa hàng XYZ Business Manager',
      created_time: '2023-01-15T10:30:00Z',
      updated_time: '2024-01-15T10:30:00Z',
      verification_status: 'verified',
      is_hidden: false,
      link: `https://business.facebook.com/${businessId}`,
      timezone_id: 7,
      two_factor_type: 'none',
      owned_ad_accounts: [
        {
          id: 'act_*********',
          name: 'Tài khoản quảng cáo chính',
          account_id: '*********',
          currency: 'VND',
          account_status: 1
        }
      ],
      owned_pages: [
        {
          id: 'page_123',
          name: 'Official Business Page',
          category: 'Business',
          can_post: true,
          fan_count: 15000,
          link: 'https://facebook.com/page_123',
          is_published: true
        }
      ],
      owned_pixels: [
        {
          id: 'pixel_123',
          name: 'Main Tracking Pixel',
          creation_time: '2023-01-15T10:30:00Z',
          code: '**********',
          is_created_by_business: true,
          is_unavailable: false,
          data_use_setting: 'ADVERTISING_AND_ANALYTICS',
          enable_automatic_matching: true,
          first_party_cookie_status: 'FIRST_PARTY_COOKIE_ENABLED',
          is_restricted_use: false
        }
      ]
    };

    return Promise.resolve(mockBM);
  }

  private getMockBusinessUsers(businessId: string): Promise<BusinessUser[]> {
    return Promise.resolve([
      {
        id: 'user_123',
        name: 'Nguyễn Văn A',
        email: '<EMAIL>',
        role: 'ADMIN',
        pending: false,
        status: 'CONFIRMED',
        permissions: ['MANAGE', 'ADVERTISE', 'ANALYZE']
      },
      {
        id: 'user_456',
        name: 'Trần Thị B',
        email: '<EMAIL>',
        role: 'EMPLOYEE',
        pending: false,
        status: 'CONFIRMED',
        permissions: ['ADVERTISE', 'ANALYZE']
      }
    ]);
  }

  private getMockBusinessAdAccounts(businessId: string): Promise<ExtendedAdAccount[]> {
    return Promise.resolve([
      {
        id: 'act_*********',
        name: 'Tài khoản quảng cáo chính',
        account_id: '*********',
        currency: 'VND',
        account_status: 1,
        access_type: 'OWNER',
        permitted_roles: ['ADMIN', 'GENERAL_USER', 'REPORTS_ONLY'],
        tasks: ['MANAGE', 'ADVERTISE', 'ANALYZE'],
        is_notifications_enabled: true,
        spend_cap: '********',
        amount_spent: '2500000',
        balance: '7500000'
      }
    ]);
  }

  private getMockBusinessPages(businessId: string): Promise<Page[]> {
    return Promise.resolve([
      {
        id: 'page_123',
        name: 'Official Business Page',
        category: 'Business',
        can_post: true,
        fan_count: 15000,
        link: 'https://facebook.com/page_123',
        is_published: true,
        verification_status: 'blue_verified'
      }
    ]);
  }

  private getMockBusinessPixels(businessId: string): Promise<Pixel[]> {
    return Promise.resolve([
      {
        id: 'pixel_123',
        name: 'Main Tracking Pixel',
        creation_time: '2023-01-15T10:30:00Z',
        code: '**********',
        is_created_by_business: true,
        is_unavailable: false,
        data_use_setting: 'ADVERTISING_AND_ANALYTICS',
        enable_automatic_matching: true,
        first_party_cookie_status: 'FIRST_PARTY_COOKIE_ENABLED',
        is_restricted_use: false
      }
    ]);
  }

  private getMockCreateAdAccount(businessId: string, accountData: any): Promise<ExtendedAdAccount> {
    return Promise.resolve({
      id: `act_${Date.now()}`,
      name: accountData.name,
      account_id: Date.now().toString(),
      currency: accountData.currency,
      account_status: 1,
      access_type: 'OWNER',
      permitted_roles: ['ADMIN', 'GENERAL_USER'],
      tasks: ['MANAGE', 'ADVERTISE', 'ANALYZE'],
      is_notifications_enabled: true
    });
  }

  private getMockShareAsset(businessId: string, assetId: string, assetType: string, targetBusinessId: string): Promise<AssetSharingRequest> {
    return Promise.resolve({
      id: `request_${Date.now()}`,
      requestor_id: businessId,
      owner_id: businessId,
      asset_id: assetId,
      asset_type: assetType as any,
      status: 'PENDING',
      created_time: new Date().toISOString(),
      updated_time: new Date().toISOString()
    });
  }
}

export const businessManagerApi = new BusinessManagerApiService();
