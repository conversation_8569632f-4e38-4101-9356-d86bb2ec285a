import { AdPerformanceMetrics, OptimizationRecommendation, AdAnalysisResult, ComparisonReport } from '../types/analytics';

export class AnalyticsEngine {
  
  // Phân tích hiệu suất tổng thể của quảng cáo
  static analyzeAdPerformance(metrics: AdPerformanceMetrics): AdAnalysisResult {
    const performance = this.calculatePerformanceRating(metrics);
    const strengths = this.identifyStrengths(metrics);
    const weaknesses = this.identifyWeaknesses(metrics);
    const recommendations = this.generateRecommendations(metrics);
    const budgetOptimization = this.optimizeBudget(metrics);

    return {
      ad: metrics,
      performance,
      strengths,
      weaknesses,
      recommendations,
      budgetOptimization
    };
  }

  // Tính toán rating hiệu suất
  private static calculatePerformanceRating(metrics: AdPerformanceMetrics): 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR' | 'CRITICAL' {
    let score = 0;
    
    // CTR scoring (0-25 points)
    if (metrics.ctr >= 3.0) score += 25;
    else if (metrics.ctr >= 2.0) score += 20;
    else if (metrics.ctr >= 1.5) score += 15;
    else if (metrics.ctr >= 1.0) score += 10;
    else score += 5;

    // ROAS scoring (0-25 points)
    if (metrics.roas >= 4.0) score += 25;
    else if (metrics.roas >= 3.0) score += 20;
    else if (metrics.roas >= 2.0) score += 15;
    else if (metrics.roas >= 1.5) score += 10;
    else score += 5;

    // CVR scoring (0-25 points)
    if (metrics.cvr >= 5.0) score += 25;
    else if (metrics.cvr >= 3.0) score += 20;
    else if (metrics.cvr >= 2.0) score += 15;
    else if (metrics.cvr >= 1.0) score += 10;
    else score += 5;

    // Quality Score (0-25 points)
    if (metrics.qualityScore >= 8) score += 25;
    else if (metrics.qualityScore >= 6) score += 20;
    else if (metrics.qualityScore >= 4) score += 15;
    else if (metrics.qualityScore >= 2) score += 10;
    else score += 5;

    if (score >= 85) return 'EXCELLENT';
    if (score >= 70) return 'GOOD';
    if (score >= 50) return 'AVERAGE';
    if (score >= 30) return 'POOR';
    return 'CRITICAL';
  }

  // Xác định điểm mạnh
  private static identifyStrengths(metrics: AdPerformanceMetrics): string[] {
    const strengths: string[] = [];

    if (metrics.ctr >= 2.5) {
      strengths.push(`CTR xuất sắc (${metrics.ctr.toFixed(2)}%) - cao hơn trung bình ngành`);
    }
    if (metrics.roas >= 3.0) {
      strengths.push(`ROAS tốt (${metrics.roas.toFixed(2)}) - mang lại lợi nhuận cao`);
    }
    if (metrics.cvr >= 3.0) {
      strengths.push(`Tỷ lệ chuyển đổi cao (${metrics.cvr.toFixed(2)}%) - audience chất lượng`);
    }
    if (metrics.qualityScore >= 7) {
      strengths.push(`Quality Score cao (${metrics.qualityScore}/10) - quảng cáo chất lượng`);
    }
    if (metrics.cpc <= 5000) {
      strengths.push(`CPC thấp (${metrics.cpc.toLocaleString('vi-VN')} ₫) - hiệu quả chi phí`);
    }
    if (metrics.reach / metrics.impressions >= 0.8) {
      strengths.push('Reach tốt - tiếp cận đối tượng rộng mà không bị lặp lại nhiều');
    }

    return strengths.length > 0 ? strengths : ['Quảng cáo đang hoạt động ổn định'];
  }

  // Xác định điểm yếu
  private static identifyWeaknesses(metrics: AdPerformanceMetrics): string[] {
    const weaknesses: string[] = [];

    if (metrics.ctr < 1.0) {
      weaknesses.push(`CTR thấp (${metrics.ctr.toFixed(2)}%) - creative không hấp dẫn hoặc targeting không chính xác`);
    }
    if (metrics.roas < 2.0) {
      weaknesses.push(`ROAS thấp (${metrics.roas.toFixed(2)}) - không đạt mục tiêu lợi nhuận`);
    }
    if (metrics.cvr < 1.0) {
      weaknesses.push(`Tỷ lệ chuyển đổi thấp (${metrics.cvr.toFixed(2)}%) - landing page hoặc offer cần cải thiện`);
    }
    if (metrics.qualityScore < 5) {
      weaknesses.push(`Quality Score thấp (${metrics.qualityScore}/10) - cần tối ưu relevance và user experience`);
    }
    if (metrics.cpc > 15000) {
      weaknesses.push(`CPC cao (${metrics.cpc.toLocaleString('vi-VN')} ₫) - cạnh tranh gay gắt hoặc targeting rộng`);
    }
    if (metrics.cpm > 50000) {
      weaknesses.push(`CPM cao (${metrics.cpm.toLocaleString('vi-VN')} ₫) - audience nhỏ hoặc creative kém hấp dẫn`);
    }

    return weaknesses;
  }

  // Tạo đề xuất tối ưu hóa
  private static generateRecommendations(metrics: AdPerformanceMetrics): OptimizationRecommendation[] {
    const recommendations: OptimizationRecommendation[] = [];

    // Budget recommendations
    if (metrics.roas >= 3.0 && metrics.ctr >= 2.0) {
      recommendations.push({
        adId: metrics.adId,
        priority: 'HIGH',
        category: 'BUDGET',
        issue: 'Quảng cáo hiệu suất cao',
        recommendation: 'Tăng ngân sách 30-50% để scale up hiệu quả',
        expectedImpact: 'Tăng conversions và doanh thu với ROI tương tự',
        estimatedBudgetChange: metrics.totalSpend * 0.4
      });
    }

    if (metrics.roas < 1.5) {
      recommendations.push({
        adId: metrics.adId,
        priority: 'HIGH',
        category: 'BUDGET',
        issue: 'ROAS thấp, không hiệu quả',
        recommendation: 'Giảm ngân sách 50% hoặc tạm dừng để tối ưu',
        expectedImpact: 'Tiết kiệm chi phí, tập trung vào ads hiệu quả hơn',
        estimatedBudgetChange: -metrics.totalSpend * 0.5
      });
    }

    // Creative recommendations
    if (metrics.ctr < 1.5) {
      recommendations.push({
        adId: metrics.adId,
        priority: 'HIGH',
        category: 'CREATIVE',
        issue: 'CTR thấp - creative không hấp dẫn',
        recommendation: 'Test creative mới: video, carousel, hoặc UGC content',
        expectedImpact: 'Cải thiện CTR 50-100%, giảm CPC',
        estimatedBudgetChange: 0
      });
    }

    // Targeting recommendations
    if (metrics.cpc > 10000 && metrics.cvr < 2.0) {
      recommendations.push({
        adId: metrics.adId,
        priority: 'MEDIUM',
        category: 'TARGETING',
        issue: 'CPC cao, CVR thấp - targeting không chính xác',
        recommendation: 'Thu hẹp audience, sử dụng lookalike hoặc retargeting',
        expectedImpact: 'Giảm CPC 20-30%, tăng CVR',
        estimatedBudgetChange: 0
      });
    }

    // Bidding recommendations
    if (metrics.qualityScore < 6) {
      recommendations.push({
        adId: metrics.adId,
        priority: 'MEDIUM',
        category: 'BIDDING',
        issue: 'Quality Score thấp',
        recommendation: 'Chuyển sang bid strategy "Highest Value" và tối ưu landing page',
        expectedImpact: 'Cải thiện Quality Score, giảm chi phí',
        estimatedBudgetChange: 0
      });
    }

    return recommendations;
  }

  // Tối ưu hóa ngân sách
  private static optimizeBudget(metrics: AdPerformanceMetrics) {
    let recommendedBudget = metrics.totalSpend;
    let reasoning = '';

    if (metrics.roas >= 4.0 && metrics.ctr >= 2.5) {
      recommendedBudget = metrics.totalSpend * 1.5;
      reasoning = 'Quảng cáo hiệu suất xuất sắc, nên tăng ngân sách để scale up';
    } else if (metrics.roas >= 2.5 && metrics.ctr >= 1.5) {
      recommendedBudget = metrics.totalSpend * 1.2;
      reasoning = 'Hiệu suất tốt, có thể tăng ngân sách nhẹ để mở rộng reach';
    } else if (metrics.roas < 1.5 || metrics.ctr < 0.8) {
      recommendedBudget = metrics.totalSpend * 0.5;
      reasoning = 'Hiệu suất kém, nên giảm ngân sách và tối ưu trước khi scale';
    } else {
      reasoning = 'Hiệu suất ổn định, duy trì ngân sách hiện tại';
    }

    return {
      currentBudget: metrics.totalSpend,
      recommendedBudget,
      reasoning
    };
  }

  // Tạo báo cáo so sánh
  static generateComparisonReport(allAds: AdPerformanceMetrics[]): ComparisonReport {
    const sortedByROAS = [...allAds].sort((a, b) => b.roas - a.roas);
    const topPerformers = sortedByROAS.slice(0, 3);
    const underPerformers = sortedByROAS.slice(-3).reverse();

    const totalSpend = allAds.reduce((sum, ad) => sum + ad.totalSpend, 0);
    const totalConversions = allAds.reduce((sum, ad) => sum + ad.conversions, 0);
    const totalRevenue = allAds.reduce((sum, ad) => sum + (ad.totalSpend * ad.roas), 0);
    const overallROAS = totalSpend > 0 ? totalRevenue / totalSpend : 0;

    const averageMetrics: Partial<AdPerformanceMetrics> = {
      ctr: allAds.reduce((sum, ad) => sum + ad.ctr, 0) / allAds.length,
      cpc: allAds.reduce((sum, ad) => sum + ad.cpc, 0) / allAds.length,
      cpm: allAds.reduce((sum, ad) => sum + ad.cpm, 0) / allAds.length,
      roas: allAds.reduce((sum, ad) => sum + ad.roas, 0) / allAds.length,
      cvr: allAds.reduce((sum, ad) => sum + ad.cvr, 0) / allAds.length,
      qualityScore: allAds.reduce((sum, ad) => sum + ad.qualityScore, 0) / allAds.length
    };

    return {
      topPerformers,
      underPerformers,
      averageMetrics,
      totalSpend,
      totalConversions,
      overallROAS
    };
  }
}