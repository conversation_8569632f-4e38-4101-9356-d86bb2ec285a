import React, { useState } from 'react';
import { X, CreditCard, Globe, DollarSign, Building2, AlertCircle, CheckCircle } from 'lucide-react';
import { businessManagerApi } from '../utils/businessManagerApi';
import { ExtendedAdAccount } from '../types/facebook';

interface CreateAdAccountModalProps {
  isOpen: boolean;
  onClose: () => void;
  businessId: string;
  onSuccess: (newAccount: ExtendedAdAccount) => void;
}

const CURRENCIES = [
  { code: 'VND', name: 'Vietnamese Dong (₫)', symbol: '₫' },
  { code: 'USD', name: 'US Dollar ($)', symbol: '$' },
  { code: 'EUR', name: 'Euro (€)', symbol: '€' },
  { code: 'GBP', name: 'British Pound (£)', symbol: '£' },
  { code: 'JPY', name: 'Japanese Yen (¥)', symbol: '¥' },
  { code: 'KRW', name: 'Korean Won (₩)', symbol: '₩' },
  { code: 'THB', name: 'Thai Baht (฿)', symbol: '฿' },
  { code: 'SGD', name: 'Singapore Dollar (S$)', symbol: 'S$' }
];

const TIMEZONES = [
  { id: 7, name: 'Asia/Ho_Chi_Minh (UTC+7)', offset: '+7' },
  { id: 0, name: 'UTC (UTC+0)', offset: '+0' },
  { id: -8, name: 'America/Los_Angeles (UTC-8)', offset: '-8' },
  { id: -5, name: 'America/New_York (UTC-5)', offset: '-5' },
  { id: 1, name: 'Europe/London (UTC+1)', offset: '+1' },
  { id: 9, name: 'Asia/Tokyo (UTC+9)', offset: '+9' },
  { id: 8, name: 'Asia/Singapore (UTC+8)', offset: '+8' }
];

export const CreateAdAccountModal: React.FC<CreateAdAccountModalProps> = ({
  isOpen,
  onClose,
  businessId,
  onSuccess
}) => {
  const [formData, setFormData] = useState({
    name: '',
    currency: 'VND',
    timezone_id: 7,
    end_advertiser: '',
    media_agency: '',
    partner: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('Tên tài khoản quảng cáo là bắt buộc');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const newAccount = await businessManagerApi.createAdAccount(businessId, {
        name: formData.name.trim(),
        currency: formData.currency,
        timezone_id: formData.timezone_id,
        end_advertiser: formData.end_advertiser.trim() || undefined,
        media_agency: formData.media_agency.trim() || undefined,
        partner: formData.partner.trim() || undefined
      });

      setSuccess(true);
      setTimeout(() => {
        onSuccess(newAccount);
        handleClose();
      }, 1500);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể tạo tài khoản quảng cáo');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      name: '',
      currency: 'VND',
      timezone_id: 7,
      end_advertiser: '',
      media_agency: '',
      partner: ''
    });
    setError(null);
    setSuccess(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <CreditCard className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Tạo tài khoản quảng cáo mới</h2>
              <p className="text-sm text-gray-600">Tạo TKQC trong Business Manager</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
              <span className="text-green-800">Tài khoản quảng cáo đã được tạo thành công!</span>
            </div>
          )}

          {/* Account Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tên tài khoản quảng cáo *
            </label>
            <div className="relative">
              <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Ví dụ: Tài khoản quảng cáo chính"
                required
              />
            </div>
          </div>

          {/* Currency */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tiền tệ *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={formData.currency}
                onChange={(e) => setFormData({ ...formData, currency: e.target.value })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                {CURRENCIES.map(currency => (
                  <option key={currency.code} value={currency.code}>
                    {currency.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Timezone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Múi giờ *
            </label>
            <div className="relative">
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              <select
                value={formData.timezone_id}
                onChange={(e) => setFormData({ ...formData, timezone_id: parseInt(e.target.value) })}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                {TIMEZONES.map(timezone => (
                  <option key={timezone.id} value={timezone.id}>
                    {timezone.name}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Optional Fields */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Thông tin bổ sung (Tùy chọn)</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  End Advertiser
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.end_advertiser}
                    onChange={(e) => setFormData({ ...formData, end_advertiser: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tên công ty quảng cáo cuối"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Media Agency
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.media_agency}
                    onChange={(e) => setFormData({ ...formData, media_agency: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tên agency truyền thông"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Partner
                </label>
                <div className="relative">
                  <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={formData.partner}
                    onChange={(e) => setFormData({ ...formData, partner: e.target.value })}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tên đối tác"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Hủy
            </button>
            <button
              type="submit"
              disabled={isLoading || success}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Đang tạo...</span>
                </>
              ) : success ? (
                <>
                  <CheckCircle className="w-4 h-4" />
                  <span>Đã tạo</span>
                </>
              ) : (
                <>
                  <CreditCard className="w-4 h-4" />
                  <span>Tạo tài khoản</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
