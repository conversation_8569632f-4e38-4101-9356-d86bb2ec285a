export interface AdPerformanceMetrics {
  adId: string;
  adName: string;
  campaignName: string;
  adSetName: string;
  
  // Chi phí (VNĐ)
  totalSpend: number;
  cpc: number;
  cpm: number;
  cpa: number;
  
  // Hiệu suất
  ctr: number;
  cvr: number;
  roas: number;
  roi: number;
  
  // Tương tác
  clicks: number;
  impressions: number;
  conversions: number;
  reach: number;
  
  // Chất lượng
  qualityScore: number;
  relevanceScore: number;
  
  // Thời gian
  dateRange: string;
  status: 'ACTIVE' | 'PAUSED' | 'ARCHIVED';
}

export interface OptimizationRecommendation {
  adId: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  category: 'BUDGET' | 'TARGETING' | 'CREATIVE' | 'BIDDING';
  issue: string;
  recommendation: string;
  expectedImpact: string;
  estimatedBudgetChange: number;
}

export interface AdAnalysisResult {
  ad: AdPerformanceMetrics;
  performance: 'EXCELLENT' | 'GOOD' | 'AVERAGE' | 'POOR' | 'CRITICAL';
  strengths: string[];
  weaknesses: string[];
  recommendations: OptimizationRecommendation[];
  budgetOptimization: {
    currentBudget: number;
    recommendedBudget: number;
    reasoning: string;
  };
}

export interface ComparisonReport {
  topPerformers: AdPerformanceMetrics[];
  underPerformers: AdPerformanceMetrics[];
  averageMetrics: Partial<AdPerformanceMetrics>;
  totalSpend: number;
  totalConversions: number;
  overallROAS: number;
}