// Client-side API service for browser environment
// This replaces direct MongoDB imports which don't work in browsers

class ClientApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
  }

  // Health check
  async getHealthStatus(): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/health`);
      return await response.json();
    } catch (error) {
      return {
        status: 'api_unavailable',
        error: error.message,
        fallback: 'Using mock data'
      };
    }
  }

  // Get campaigns from API or fallback to mock
  async getCampaigns(accountId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/accounts/${accountId}/campaigns`);
      if (response.ok) {
        const result = await response.json();
        return result.data || [];
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock campaigns');
      return this.getMockCampaigns();
    }
  }

  // Get ad sets from API or fallback to mock
  async getAdSets(accountId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/accounts/${accountId}/adsets`);
      if (response.ok) {
        const result = await response.json();
        return result.data || [];
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock adsets');
      return this.getMockAdSets();
    }
  }

  // Get ads from API or fallback to mock
  async getAds(accountId: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/accounts/${accountId}/ads`);
      if (response.ok) {
        const result = await response.json();
        return result.data || [];
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock ads');
      return this.getMockAds();
    }
  }

  // Get insights from API or fallback to mock
  async getInsights(itemId: string, level: string, datePreset: string): Promise<any[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/${itemId}/insights?level=${level}&date_preset=${datePreset}`
      );
      if (response.ok) {
        const result = await response.json();
        return result.data || [];
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock insights');
      return this.getMockInsights();
    }
  }

  // Get performance summary from API or fallback to mock
  async getPerformanceSummary(
    accountId: string,
    itemType: string,
    itemId: string,
    datePreset: string = 'last_7d'
  ): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/api/${itemId}/performance?accountId=${accountId}&itemType=${itemType}&datePreset=${datePreset}`
      );
      if (response.ok) {
        return await response.json();
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock performance summary');
      return this.getMockPerformanceSummary();
    }
  }

  // Get account overview from API or fallback to mock
  async getAccountOverview(accountId: string, datePreset: string = 'last_7d'): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/api/accounts/${accountId}/overview?datePreset=${datePreset}`);
      if (response.ok) {
        return await response.json();
      }
      throw new Error('API not available');
    } catch (error) {
      console.warn('API not available, using mock account overview');
      return this.getMockAccountOverview(accountId);
    }
  }

  // Manual sync trigger
  async triggerManualSync(accountId: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/sync/${accountId}`, {
        method: 'POST'
      });
      return await response.json();
    } catch (error) {
      return {
        status: 'error',
        message: 'Manual sync not available - API server not running'
      };
    }
  }

  // Mock data generators for fallback
  private getMockCampaigns(): any[] {
    return [
      {
        id: 'campaign_001',
        name: 'Black Friday 2024 Campaign',
        status: 'ACTIVE',
        objective: 'CONVERSIONS',
        created_time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'campaign_002',
        name: 'Christmas Sale Campaign',
        status: 'ACTIVE',
        objective: 'TRAFFIC',
        created_time: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'campaign_003',
        name: 'New Year Promotion',
        status: 'PAUSED',
        objective: 'REACH',
        created_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      }
    ];
  }

  private getMockAdSets(): any[] {
    return [
      {
        id: 'adset_001',
        name: 'Men 25-35 Interest Targeting',
        status: 'ACTIVE',
        campaign_id: 'campaign_001',
        created_time: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'adset_002',
        name: 'Women 18-30 Lookalike Audience',
        status: 'ACTIVE',
        campaign_id: 'campaign_001',
        created_time: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'adset_003',
        name: 'Retargeting Website Visitors',
        status: 'ACTIVE',
        campaign_id: 'campaign_002',
        created_time: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      }
    ];
  }

  private getMockAds(): any[] {
    return [
      {
        id: 'ad_001',
        name: 'Video Ad - Product Demo',
        status: 'ACTIVE',
        adset_id: 'adset_001',
        campaign_id: 'campaign_001',
        creative: {
          title: 'Amazing Product Sale!',
          body: 'Get 50% off on all products. Limited time offer!',
          image_url: 'https://via.placeholder.com/400x300'
        },
        created_time: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'ad_002',
        name: 'Carousel Ad - Multiple Products',
        status: 'ACTIVE',
        adset_id: 'adset_002',
        campaign_id: 'campaign_001',
        creative: {
          title: 'Shop Our Best Sellers',
          body: 'Discover our top-rated products with amazing discounts!',
          image_url: 'https://via.placeholder.com/400x300'
        },
        created_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      },
      {
        id: 'ad_003',
        name: 'Single Image Ad - Hero Product',
        status: 'ACTIVE',
        adset_id: 'adset_003',
        campaign_id: 'campaign_002',
        creative: {
          title: 'Christmas Special Offer',
          body: 'Perfect gift for your loved ones. Order now!',
          image_url: 'https://via.placeholder.com/400x300'
        },
        created_time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
        updated_time: new Date().toISOString()
      }
    ];
  }

  private getMockInsights(): any[] {
    const insights = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      insights.push({
        date_start: date.toISOString().split('T')[0],
        date_stop: date.toISOString().split('T')[0],
        impressions: (Math.random() * 5000 + 2000).toString(),
        clicks: (Math.random() * 200 + 50).toString(),
        spend: (Math.random() * 500000 + 200000).toString(),
        reach: (Math.random() * 4000 + 1500).toString(),
        frequency: (Math.random() * 2 + 1).toString(),
        ctr: (Math.random() * 4 + 1).toString(),
        cpc: (Math.random() * 10000 + 5000).toString(),
        cpm: (Math.random() * 50000 + 30000).toString(),
        actions: [
          { action_type: 'purchase', value: Math.floor(Math.random() * 10 + 1).toString() },
          { action_type: 'add_to_cart', value: Math.floor(Math.random() * 30 + 10).toString() }
        ]
      });
    }
    return insights;
  }

  private getMockPerformanceSummary(): any {
    const impressions = Math.random() * 10000 + 5000;
    const clicks = Math.random() * 500 + 100;
    const spend = Math.random() * 1000000 + 500000;
    const reach = Math.random() * 8000 + 4000;
    
    const ctr = (clicks / impressions) * 100;
    const cpc = spend / clicks;
    const cpm = (spend / impressions) * 1000;
    
    let performance = 'poor';
    if (ctr >= 3.0 && cpc <= 8000) {
      performance = 'excellent';
    } else if (ctr >= 2.0 && cpc <= 12000) {
      performance = 'good';
    } else if (ctr >= 1.0 && cpc <= 20000) {
      performance = 'average';
    }
    
    return {
      impressions: Math.round(impressions),
      clicks: Math.round(clicks),
      spend: Math.round(spend),
      reach: Math.round(reach),
      ctr: Math.round(ctr * 100) / 100,
      cpc: Math.round(cpc),
      cpm: Math.round(cpm),
      performance
    };
  }

  private getMockAccountOverview(accountId: string): any {
    return {
      account_id: accountId,
      date_range: 'last_7d',
      summary: {
        campaigns_count: 3,
        adsets_count: 3,
        ads_count: 3,
        total_impressions: 45000,
        total_clicks: 1350,
        total_spend: 2250000,
        total_reach: 36000,
        average_ctr: 3.0,
        average_cpc: 1666.67,
        average_cpm: 50000
      },
      last_updated: new Date()
    };
  }
}

export const clientApiService = new ClientApiService();
