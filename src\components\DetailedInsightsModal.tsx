import React, { useState, useEffect } from 'react';
import { 
  X, 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  MousePointer, 
  DollarSign, 
  Users, 
  Target,
  Calendar,
  BarChart3,
  Activity,
  Zap,
  Award,
  AlertTriangle
} from 'lucide-react';
import { facebookApi } from '../utils/facebookApi';
import { Insights } from '../types/facebook';

interface DetailedInsightsModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string;
  itemName: string;
  itemType: 'campaign' | 'adset' | 'ad';
  accountId: string;
}

interface DetailedMetrics {
  impressions: number;
  clicks: number;
  spend: number;
  reach: number;
  frequency: number;
  ctr: number;
  cpc: number;
  cpm: number;
  cpp: number;
  unique_clicks: number;
  unique_ctr: number;
  cost_per_unique_click: number;
  actions: Array<{ action_type: string; value: number }>;
  cost_per_action_type: Array<{ action_type: string; value: number }>;
}

export const DetailedInsightsModal: React.FC<DetailedInsightsModalProps> = ({
  isOpen,
  onClose,
  itemId,
  itemName,
  itemType,
  accountId
}) => {
  const [insights, setInsights] = useState<Insights[]>([]);
  const [metrics, setMetrics] = useState<DetailedMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('last_7d');

  useEffect(() => {
    if (isOpen) {
      loadDetailedInsights();
    }
  }, [isOpen, itemId, dateRange]);

  const loadDetailedInsights = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Try API first, fallback to Facebook API
      let insightsData;

      try {
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';
        const response = await fetch(
          `${apiBaseUrl}/api/${itemId}/insights?level=${itemType}&date_preset=${dateRange}`
        );

        if (response.ok) {
          const result = await response.json();
          insightsData = result.data || [];
        } else {
          throw new Error('API not available');
        }
      } catch (apiError) {
        console.warn('API not available, using Facebook API:', apiError);
        insightsData = await facebookApi.getInsights(itemId, 'ad', dateRange);
      }

      setInsights(insightsData);
      
      // Calculate aggregated metrics
      const aggregated = insightsData.reduce((acc, insight) => {
        acc.impressions += parseInt(insight.impressions) || 0;
        acc.clicks += parseInt(insight.clicks) || 0;
        acc.spend += parseFloat(insight.spend) || 0;
        acc.reach += parseInt(insight.reach) || 0;
        acc.frequency += parseFloat(insight.frequency) || 0;
        acc.unique_clicks += parseInt(insight.unique_clicks) || 0;
        
        // Process actions
        if (insight.actions) {
          insight.actions.forEach(action => {
            const existingAction = acc.actions.find(a => a.action_type === action.action_type);
            if (existingAction) {
              existingAction.value += parseInt(action.value) || 0;
            } else {
              acc.actions.push({ action_type: action.action_type, value: parseInt(action.value) || 0 });
            }
          });
        }

        return acc;
      }, {
        impressions: 0,
        clicks: 0,
        spend: 0,
        reach: 0,
        frequency: 0,
        unique_clicks: 0,
        actions: [] as Array<{ action_type: string; value: number }>,
        cost_per_action_type: [] as Array<{ action_type: string; value: number }>
      });

      // Calculate derived metrics
      const calculatedMetrics: DetailedMetrics = {
        ...aggregated,
        frequency: aggregated.reach > 0 ? aggregated.impressions / aggregated.reach : 0,
        ctr: aggregated.impressions > 0 ? (aggregated.clicks / aggregated.impressions) * 100 : 0,
        cpc: aggregated.clicks > 0 ? aggregated.spend / aggregated.clicks : 0,
        cpm: aggregated.impressions > 0 ? (aggregated.spend / aggregated.impressions) * 1000 : 0,
        cpp: aggregated.reach > 0 ? aggregated.spend / aggregated.reach : 0,
        unique_ctr: aggregated.reach > 0 ? (aggregated.unique_clicks / aggregated.reach) * 100 : 0,
        cost_per_unique_click: aggregated.unique_clicks > 0 ? aggregated.spend / aggregated.unique_clicks : 0,
        cost_per_action_type: aggregated.actions.map(action => ({
          action_type: action.action_type,
          value: action.value > 0 ? aggregated.spend / action.value : 0
        }))
      };

      setMetrics(calculatedMetrics);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể tải dữ liệu chi tiết');
    } finally {
      setIsLoading(false);
    }
  };

  const getActionLabel = (actionType: string) => {
    const labels: { [key: string]: string } = {
      'purchase': 'Mua hàng',
      'add_to_cart': 'Thêm vào giỏ',
      'view_content': 'Xem nội dung',
      'initiate_checkout': 'Bắt đầu thanh toán',
      'lead': 'Khách hàng tiềm năng',
      'complete_registration': 'Đăng ký hoàn tất',
      'page_engagement': 'Tương tác trang',
      'post_engagement': 'Tương tác bài viết',
      'video_view': 'Xem video',
      'link_click': 'Nhấp liên kết'
    };
    return labels[actionType] || actionType.replace(/_/g, ' ');
  };

  const getItemTypeLabel = (type: string) => {
    const labels = {
      'campaign': 'Chiến dịch',
      'adset': 'Nhóm quảng cáo',
      'ad': 'Quảng cáo'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  const getPerformanceIndicator = (value: number, type: 'ctr' | 'cpc' | 'cpm') => {
    let threshold = 0;
    let isGood = false;

    switch (type) {
      case 'ctr':
        threshold = 2.0;
        isGood = value >= threshold;
        break;
      case 'cpc':
        threshold = 10000; // 10k VND
        isGood = value <= threshold;
        break;
      case 'cpm':
        threshold = 50000; // 50k VND
        isGood = value <= threshold;
        break;
    }

    return isGood ? (
      <TrendingUp className="w-4 h-4 text-green-600" />
    ) : (
      <TrendingDown className="w-4 h-4 text-red-600" />
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Chi tiết hiệu suất</h2>
              <p className="text-sm text-gray-600">{getItemTypeLabel(itemType)}: {itemName}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="today">Hôm nay</option>
              <option value="yesterday">Hôm qua</option>
              <option value="last_3d">3 ngày qua</option>
              <option value="last_7d">7 ngày qua</option>
              <option value="last_14d">14 ngày qua</option>
              <option value="last_30d">30 ngày qua</option>
            </select>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-gray-600">Đang tải dữ liệu chi tiết...</span>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <span className="text-red-800">{error}</span>
            </div>
          ) : metrics ? (
            <div className="space-y-6">
              {/* Key Metrics Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-600 text-sm font-medium">Lượt hiển thị</p>
                      <p className="text-2xl font-bold text-blue-900">{metrics.impressions.toLocaleString()}</p>
                    </div>
                    <Eye className="w-8 h-8 text-blue-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-600 text-sm font-medium">Lượt nhấp</p>
                      <p className="text-2xl font-bold text-green-900">{metrics.clicks.toLocaleString()}</p>
                    </div>
                    <MousePointer className="w-8 h-8 text-green-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-600 text-sm font-medium">Chi phí</p>
                      <p className="text-2xl font-bold text-purple-900">{formatCurrency(metrics.spend)}</p>
                    </div>
                    <DollarSign className="w-8 h-8 text-purple-600" />
                  </div>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-orange-600 text-sm font-medium">Tiếp cận</p>
                      <p className="text-2xl font-bold text-orange-900">{metrics.reach.toLocaleString()}</p>
                    </div>
                    <Users className="w-8 h-8 text-orange-600" />
                  </div>
                </div>
              </div>

              {/* Performance Metrics */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  Chỉ số hiệu suất
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">CTR (Tỷ lệ nhấp)</span>
                      {getPerformanceIndicator(metrics.ctr, 'ctr')}
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{metrics.ctr.toFixed(2)}%</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {metrics.ctr >= 2.0 ? 'Tốt' : 'Cần cải thiện'} (Chuẩn: ≥2.0%)
                    </p>
                  </div>

                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">CPC (Chi phí/nhấp)</span>
                      {getPerformanceIndicator(metrics.cpc, 'cpc')}
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.cpc)}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {metrics.cpc <= 10000 ? 'Tốt' : 'Cao'} (Chuẩn: ≤10k VND)
                    </p>
                  </div>

                  <div className="bg-white rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-600">CPM (Chi phí/1000 hiển thị)</span>
                      {getPerformanceIndicator(metrics.cpm, 'cpm')}
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.cpm)}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {metrics.cpm <= 50000 ? 'Tốt' : 'Cao'} (Chuẩn: ≤50k VND)
                    </p>
                  </div>
                </div>
              </div>

              {/* Actions & Conversions */}
              {metrics.actions.length > 0 && (
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2" />
                    Hành động & Chuyển đổi
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {metrics.actions.map((action, index) => {
                      const costPerAction = metrics.cost_per_action_type.find(
                        c => c.action_type === action.action_type
                      );
                      
                      return (
                        <div key={index} className="bg-white rounded-lg p-4 border border-gray-200">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-medium text-gray-600">
                              {getActionLabel(action.action_type)}
                            </span>
                            <Award className="w-4 h-4 text-yellow-600" />
                          </div>
                          <p className="text-xl font-bold text-gray-900">{action.value.toLocaleString()}</p>
                          {costPerAction && (
                            <p className="text-xs text-gray-500 mt-1">
                              Chi phí: {formatCurrency(costPerAction.value)}
                            </p>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Additional Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Chỉ số bổ sung</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tần suất hiển thị</span>
                      <span className="font-medium">{metrics.frequency.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Nhấp duy nhất</span>
                      <span className="font-medium">{metrics.unique_clicks.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">CTR duy nhất</span>
                      <span className="font-medium">{metrics.unique_ctr.toFixed(2)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Chi phí/nhấp duy nhất</span>
                      <span className="font-medium">{formatCurrency(metrics.cost_per_unique_click)}</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Thông tin thời gian</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Khoảng thời gian</span>
                      <span className="font-medium">{insights.length} ngày</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Chi phí trung bình/ngày</span>
                      <span className="font-medium">
                        {formatCurrency(insights.length > 0 ? metrics.spend / insights.length : 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Hiển thị trung bình/ngày</span>
                      <span className="font-medium">
                        {Math.round(insights.length > 0 ? metrics.impressions / insights.length : 0).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Không có dữ liệu chi tiết</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
