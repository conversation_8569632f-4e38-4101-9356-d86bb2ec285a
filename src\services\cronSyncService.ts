import cron from 'node-cron';
import { facebookApi } from '../utils/facebookApi';
import { mongoDbService, CampaignInsights, AdSetInsights, AdInsights } from '../utils/mongoDbService';

interface SyncConfig {
  accountIds: string[];
  accessToken: string;
  syncIntervalHours: number;
  maxDaysHistory: number;
  enabledSyncTypes: ('campaigns' | 'adsets' | 'ads')[];
}

class CronSyncService {
  private isRunning = false;
  private config: SyncConfig;
  private cronJob: cron.ScheduledTask | null = null;

  constructor(config: SyncConfig) {
    this.config = config;
  }

  async start(): Promise<void> {
    if (this.cronJob) {
      console.log('⚠️ Cron job already running');
      return;
    }

    // Connect to MongoDB
    await mongoDbService.connect();
    await mongoDbService.createIndexes();

    // Schedule cron job every hour
    const cronExpression = `0 */${this.config.syncIntervalHours} * * *`;
    
    this.cronJob = cron.schedule(cronExpression, async () => {
      if (this.isRunning) {
        console.log('⚠️ Previous sync still running, skipping...');
        return;
      }

      console.log('🚀 Starting scheduled Facebook Ads sync...');
      await this.syncAllData();
    }, {
      scheduled: true,
      timezone: 'Asia/Ho_Chi_Minh'
    });

    console.log(`✅ Cron job scheduled: ${cronExpression}`);
    
    // Run initial sync
    await this.syncAllData();
  }

  async stop(): Promise<void> {
    if (this.cronJob) {
      this.cronJob.stop();
      this.cronJob = null;
      console.log('✅ Cron job stopped');
    }
    
    await mongoDbService.disconnect();
  }

  async syncAllData(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️ Sync already in progress');
      return;
    }

    this.isRunning = true;
    const startTime = new Date();
    
    try {
      console.log(`🔄 Starting sync for ${this.config.accountIds.length} accounts`);
      
      for (const accountId of this.config.accountIds) {
        await this.syncAccountData(accountId);
      }

      const duration = Date.now() - startTime.getTime();
      console.log(`✅ Sync completed in ${duration}ms`);
      
    } catch (error) {
      console.error('❌ Sync failed:', error);
    } finally {
      this.isRunning = false;
    }
  }

  private async syncAccountData(accountId: string): Promise<void> {
    console.log(`📊 Syncing account: ${accountId}`);
    
    // Set Facebook API access token
    facebookApi.setAccessToken(this.config.accessToken);
    
    const dateRange = this.getDateRange();
    
    try {
      // Sync campaigns
      if (this.config.enabledSyncTypes.includes('campaigns')) {
        await this.syncCampaigns(accountId, dateRange);
      }

      // Sync ad sets
      if (this.config.enabledSyncTypes.includes('adsets')) {
        await this.syncAdSets(accountId, dateRange);
      }

      // Sync ads
      if (this.config.enabledSyncTypes.includes('ads')) {
        await this.syncAds(accountId, dateRange);
      }

    } catch (error) {
      console.error(`❌ Failed to sync account ${accountId}:`, error);
    }
  }

  private async syncCampaigns(accountId: string, dateRange: { start: Date; end: Date }): Promise<void> {
    const logId = await mongoDbService.logSyncStart(accountId, 'campaigns');
    let recordsSynced = 0;
    
    try {
      console.log(`📈 Syncing campaigns for account: ${accountId}`);
      
      // Get campaigns from Facebook API
      const campaigns = await facebookApi.getCampaigns(accountId);
      
      const campaignInsights: CampaignInsights[] = [];
      
      for (const campaign of campaigns) {
        try {
          // Get insights for each campaign
          const insights = await facebookApi.getInsights(campaign.id, 'campaign', 'last_30d');
          
          for (const insight of insights) {
            const campaignInsight: CampaignInsights = {
              campaign_id: campaign.id,
              campaign_name: campaign.name,
              account_id: accountId,
              date: new Date(insight.date_start),
              impressions: parseInt(insight.impressions) || 0,
              clicks: parseInt(insight.clicks) || 0,
              spend: parseFloat(insight.spend) || 0,
              reach: parseInt(insight.reach) || 0,
              frequency: parseFloat(insight.frequency) || 0,
              ctr: parseFloat(insight.ctr) || 0,
              cpc: parseFloat(insight.cpc) || 0,
              cpm: parseFloat(insight.cpm) || 0,
              actions: insight.actions || [],
              cost_per_action_type: insight.cost_per_action_type || [],
              created_at: new Date(),
              updated_at: new Date()
            };
            
            campaignInsights.push(campaignInsight);
          }
          
          recordsSynced++;
          
          // Add delay to respect rate limits
          await this.delay(100);
          
        } catch (error) {
          console.error(`❌ Failed to sync campaign ${campaign.id}:`, error);
        }
      }
      
      // Save to MongoDB
      if (campaignInsights.length > 0) {
        await mongoDbService.saveCampaignInsights(campaignInsights);
      }
      
      await mongoDbService.logSyncComplete(logId, recordsSynced);
      console.log(`✅ Synced ${recordsSynced} campaigns`);
      
    } catch (error) {
      await mongoDbService.logSyncComplete(logId, recordsSynced, error.message);
      throw error;
    }
  }

  private async syncAdSets(accountId: string, dateRange: { start: Date; end: Date }): Promise<void> {
    const logId = await mongoDbService.logSyncStart(accountId, 'adsets');
    let recordsSynced = 0;
    
    try {
      console.log(`📊 Syncing ad sets for account: ${accountId}`);
      
      const adSets = await facebookApi.getAdSets(accountId);
      const adSetInsights: AdSetInsights[] = [];
      
      for (const adSet of adSets) {
        try {
          const insights = await facebookApi.getInsights(adSet.id, 'adset', 'last_30d');
          
          for (const insight of insights) {
            const adSetInsight: AdSetInsights = {
              adset_id: adSet.id,
              adset_name: adSet.name,
              campaign_id: adSet.campaign_id,
              account_id: accountId,
              date: new Date(insight.date_start),
              impressions: parseInt(insight.impressions) || 0,
              clicks: parseInt(insight.clicks) || 0,
              spend: parseFloat(insight.spend) || 0,
              reach: parseInt(insight.reach) || 0,
              frequency: parseFloat(insight.frequency) || 0,
              ctr: parseFloat(insight.ctr) || 0,
              cpc: parseFloat(insight.cpc) || 0,
              cpm: parseFloat(insight.cpm) || 0,
              actions: insight.actions || [],
              created_at: new Date(),
              updated_at: new Date()
            };
            
            adSetInsights.push(adSetInsight);
          }
          
          recordsSynced++;
          await this.delay(100);
          
        } catch (error) {
          console.error(`❌ Failed to sync adset ${adSet.id}:`, error);
        }
      }
      
      if (adSetInsights.length > 0) {
        await mongoDbService.saveAdSetInsights(adSetInsights);
      }
      
      await mongoDbService.logSyncComplete(logId, recordsSynced);
      console.log(`✅ Synced ${recordsSynced} ad sets`);
      
    } catch (error) {
      await mongoDbService.logSyncComplete(logId, recordsSynced, error.message);
      throw error;
    }
  }

  private async syncAds(accountId: string, dateRange: { start: Date; end: Date }): Promise<void> {
    const logId = await mongoDbService.logSyncStart(accountId, 'ads');
    let recordsSynced = 0;
    
    try {
      console.log(`🎯 Syncing ads for account: ${accountId}`);
      
      const ads = await facebookApi.getAds(accountId);
      const adInsights: AdInsights[] = [];
      
      for (const ad of ads) {
        try {
          const insights = await facebookApi.getInsights(ad.id, 'ad', 'last_30d');
          
          for (const insight of insights) {
            const adInsight: AdInsights = {
              ad_id: ad.id,
              ad_name: ad.name,
              adset_id: ad.adset_id,
              campaign_id: ad.campaign_id || '',
              account_id: accountId,
              date: new Date(insight.date_start),
              impressions: parseInt(insight.impressions) || 0,
              clicks: parseInt(insight.clicks) || 0,
              spend: parseFloat(insight.spend) || 0,
              reach: parseInt(insight.reach) || 0,
              frequency: parseFloat(insight.frequency) || 0,
              ctr: parseFloat(insight.ctr) || 0,
              cpc: parseFloat(insight.cpc) || 0,
              cpm: parseFloat(insight.cpm) || 0,
              actions: insight.actions || [],
              creative: ad.creative ? {
                title: ad.creative.title || '',
                body: ad.creative.body || '',
                image_url: ad.creative.image_url,
                video_url: ad.creative.video_url
              } : undefined,
              created_at: new Date(),
              updated_at: new Date()
            };
            
            adInsights.push(adInsight);
          }
          
          recordsSynced++;
          await this.delay(100);
          
        } catch (error) {
          console.error(`❌ Failed to sync ad ${ad.id}:`, error);
        }
      }
      
      if (adInsights.length > 0) {
        await mongoDbService.saveAdInsights(adInsights);
      }
      
      await mongoDbService.logSyncComplete(logId, recordsSynced);
      console.log(`✅ Synced ${recordsSynced} ads`);
      
    } catch (error) {
      await mongoDbService.logSyncComplete(logId, recordsSynced, error.message);
      throw error;
    }
  }

  private getDateRange(): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - this.config.maxDaysHistory);
    
    return { start, end };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Manual sync methods
  async manualSync(accountId: string): Promise<void> {
    console.log(`🔄 Manual sync triggered for account: ${accountId}`);
    await this.syncAccountData(accountId);
  }

  async getLastSyncStatus(accountId: string): Promise<any> {
    const campaignSync = await mongoDbService.getLastSyncTime(accountId, 'campaigns');
    const adsetSync = await mongoDbService.getLastSyncTime(accountId, 'adsets');
    const adSync = await mongoDbService.getLastSyncTime(accountId, 'ads');
    
    return {
      account_id: accountId,
      last_sync: {
        campaigns: campaignSync,
        adsets: adsetSync,
        ads: adSync
      },
      next_sync: this.getNextSyncTime()
    };
  }

  private getNextSyncTime(): Date {
    const next = new Date();
    next.setHours(next.getHours() + this.config.syncIntervalHours);
    return next;
  }

  // Health check
  async getHealthStatus(): Promise<any> {
    const mongoHealth = await mongoDbService.getHealthCheck();
    
    return {
      cron_service: {
        status: this.cronJob ? 'running' : 'stopped',
        is_syncing: this.isRunning,
        sync_interval_hours: this.config.syncIntervalHours,
        accounts_count: this.config.accountIds.length
      },
      mongodb: mongoHealth,
      last_sync: new Date()
    };
  }
}

// Export singleton instance
export const createCronSyncService = (config: SyncConfig) => {
  return new CronSyncService(config);
};

// Default configuration
export const defaultSyncConfig: SyncConfig = {
  accountIds: [], // Will be populated from environment or user settings
  accessToken: '', // Will be set from user authentication
  syncIntervalHours: 1, // Sync every hour
  maxDaysHistory: 30, // Keep 30 days of data
  enabledSyncTypes: ['campaigns', 'adsets', 'ads']
};
