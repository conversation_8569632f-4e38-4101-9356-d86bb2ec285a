import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Database, Server } from 'lucide-react';
import { clientApiService } from '../utils/clientApiService';

interface ApiStatusIndicatorProps {
  className?: string;
}

export const ApiStatusIndicator: React.FC<ApiStatusIndicatorProps> = ({ className = '' }) => {
  const [status, setStatus] = useState<{
    api: 'connected' | 'disconnected' | 'checking';
    database: 'connected' | 'disconnected' | 'unknown';
    cron: 'running' | 'stopped' | 'unknown';
    lastCheck: Date | null;
  }>({
    api: 'checking',
    database: 'unknown',
    cron: 'unknown',
    lastCheck: null
  });

  const checkApiStatus = async () => {
    try {
      const health = await clientApiService.getHealthStatus();
      
      if (health.status === 'api_unavailable') {
        setStatus({
          api: 'disconnected',
          database: 'unknown',
          cron: 'unknown',
          lastCheck: new Date()
        });
      } else {
        setStatus({
          api: 'connected',
          database: health.services?.database?.status === 'healthy' ? 'connected' : 'disconnected',
          cron: health.services?.cron_service?.status === 'running' ? 'running' : 'stopped',
          lastCheck: new Date()
        });
      }
    } catch (error) {
      setStatus({
        api: 'disconnected',
        database: 'unknown',
        cron: 'unknown',
        lastCheck: new Date()
      });
    }
  };

  useEffect(() => {
    checkApiStatus();
    
    // Check status every 30 seconds
    const interval = setInterval(checkApiStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
      case 'running':
        return 'text-green-500';
      case 'disconnected':
      case 'stopped':
        return 'text-red-500';
      case 'checking':
        return 'text-yellow-500';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (type: 'api' | 'database' | 'cron') => {
    const iconClass = `w-4 h-4 ${getStatusColor(status[type])}`;
    
    switch (type) {
      case 'api':
        return status.api === 'connected' ? 
          <Wifi className={iconClass} /> : 
          <WifiOff className={iconClass} />;
      case 'database':
        return <Database className={iconClass} />;
      case 'cron':
        return <Server className={iconClass} />;
    }
  };

  const getStatusText = (type: 'api' | 'database' | 'cron') => {
    const statusValue = status[type];
    
    switch (statusValue) {
      case 'connected':
        return 'Kết nối';
      case 'disconnected':
        return 'Mất kết nối';
      case 'running':
        return 'Đang chạy';
      case 'stopped':
        return 'Dừng';
      case 'checking':
        return 'Kiểm tra...';
      default:
        return 'Không rõ';
    }
  };

  const isUsingMockMode = status.api === 'disconnected';

  return (
    <div className={`bg-white rounded-lg shadow-sm border p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-gray-900">Trạng thái hệ thống</h3>
        <button
          onClick={checkApiStatus}
          className="text-xs text-blue-600 hover:text-blue-800"
        >
          Làm mới
        </button>
      </div>
      
      <div className="space-y-2">
        {/* API Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon('api')}
            <span className="text-sm text-gray-700">API Server</span>
          </div>
          <span className={`text-xs font-medium ${getStatusColor(status.api)}`}>
            {getStatusText('api')}
          </span>
        </div>
        
        {/* Database Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon('database')}
            <span className="text-sm text-gray-700">MongoDB</span>
          </div>
          <span className={`text-xs font-medium ${getStatusColor(status.database)}`}>
            {getStatusText('database')}
          </span>
        </div>
        
        {/* Cron Status */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon('cron')}
            <span className="text-sm text-gray-700">Cron Sync</span>
          </div>
          <span className={`text-xs font-medium ${getStatusColor(status.cron)}`}>
            {getStatusText('cron')}
          </span>
        </div>
      </div>
      
      {/* Mock Mode Warning */}
      {isUsingMockMode && (
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <span className="text-xs text-yellow-800">
              Đang sử dụng dữ liệu mẫu
            </span>
          </div>
          <p className="text-xs text-yellow-700 mt-1">
            API server chưa khởi động. Dữ liệu hiển thị là mock data.
          </p>
        </div>
      )}
      
      {/* Last Check */}
      {status.lastCheck && (
        <div className="mt-3 pt-2 border-t border-gray-100">
          <p className="text-xs text-gray-500">
            Cập nhật lần cuối: {status.lastCheck.toLocaleTimeString('vi-VN')}
          </p>
        </div>
      )}
      
      {/* Quick Actions */}
      {status.api === 'connected' && (
        <div className="mt-3 pt-2 border-t border-gray-100">
          <button
            onClick={async () => {
              try {
                const result = await clientApiService.triggerManualSync('act_123456789');
                console.log('Manual sync result:', result);
                // You could show a toast notification here
              } catch (error) {
                console.error('Manual sync failed:', error);
              }
            }}
            className="text-xs text-blue-600 hover:text-blue-800"
          >
            Đồng bộ thủ công
          </button>
        </div>
      )}
    </div>
  );
};
