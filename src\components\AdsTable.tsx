import React, { useState } from 'react';
import { Ad } from '../types/facebook';
import { Calendar, Play, Pause, Square, Image, BarChart3, TrendingUp } from 'lucide-react';
import { DetailedInsightsModal } from './DetailedInsightsModal';

interface AdsTableProps {
  ads: Ad[];
  accountId?: string;
}

export const AdsTable: React.FC<AdsTableProps> = ({ ads, accountId }) => {
  const [sortField, setSortField] = useState<keyof Ad>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedAd, setSelectedAd] = useState<Ad | null>(null);
  const [showInsightsModal, setShowInsightsModal] = useState(false);

  const handleSort = (field: keyof Ad) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedAds = [...ads].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Play className="w-4 h-4 text-green-600" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-600" />;
      default:
        return <Square className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewInsights = (ad: Ad) => {
    setSelectedAd(ad);
    setShowInsightsModal(true);
  };

  if (ads.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <Image className="w-10 h-10 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">Không tìm thấy quảng cáo</h3>
        <p className="text-gray-600">
          Không có quảng cáo nào cho tài khoản đã chọn.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto rounded-xl border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
          <tr>
            <th 
              onClick={() => handleSort('name')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Tên quảng cáo
            </th>
            <th 
              onClick={() => handleSort('status')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Trạng thái
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Creative
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              ID Nhóm quảng cáo
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              ID Chiến dịch
            </th>
            <th
              onClick={() => handleSort('created_time')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Ngày tạo
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Hành động
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {sortedAds.map((ad) => (
            <tr key={ad.id} className="hover:bg-blue-50/50 transition-colors">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div>
                    <div className="text-sm font-bold text-gray-900 mb-1">{ad.name}</div>
                    <div className="text-xs text-gray-500 font-mono">ID: {ad.id}</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(ad.status)}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ad.status)}`}>
                    {ad.status}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center space-x-2">
                  <Image className="w-4 h-4 text-gray-400" />
                  <span className="font-medium">{ad.creative?.name || 'Chưa có'}</span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500 font-mono">
                {ad.adset_id}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-xs text-gray-500 font-mono">
                {ad.campaign_id}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="font-medium">{new Date(ad.created_time).toLocaleDateString('vi-VN')}</span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleViewInsights(ad)}
                    className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    title="Xem chi tiết hiệu suất"
                  >
                    <BarChart3 className="w-3 h-3 mr-1" />
                    Chi tiết
                  </button>
                  <button
                    className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-xs font-medium rounded-lg hover:bg-green-700 transition-colors"
                    title="Phân tích hiệu suất"
                  >
                    <TrendingUp className="w-3 h-3 mr-1" />
                    Phân tích
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>

    {/* Detailed Insights Modal */}
    {selectedAd && accountId && (
      <DetailedInsightsModal
        isOpen={showInsightsModal}
        onClose={() => {
          setShowInsightsModal(false);
          setSelectedAd(null);
        }}
        itemId={selectedAd.id}
        itemName={selectedAd.name}
        itemType="ad"
        accountId={accountId}
      />
    )}
  </div>
  );
};