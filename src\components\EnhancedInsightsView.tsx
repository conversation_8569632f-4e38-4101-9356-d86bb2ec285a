import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON><PERSON>3,
  TrendingUp,
  <PERSON>,
  MousePointer,
  DollarSign,
  Users,
  Target,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Grid,
  List,
  Search
} from 'lucide-react';
import { facebookApi } from '../utils/facebookApi';
import { clientApiService } from '../utils/clientApiService';
import { Campaign, AdSet, Ad } from '../types/facebook';
import { PerformanceSummaryCard } from './PerformanceSummaryCard';
import { DetailedInsightsModal } from './DetailedInsightsModal';

interface EnhancedInsightsViewProps {
  accountId: string;
  campaigns: Campaign[];
  adSets: AdSet[];
  ads: Ad[];
}

type ViewMode = 'grid' | 'list';
type ItemType = 'campaigns' | 'adsets' | 'ads';

export const EnhancedInsightsView: React.FC<EnhancedInsightsViewProps> = ({
  accountId,
  campaigns,
  adSets,
  ads
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [activeItemType, setActiveItemType] = useState<ItemType>('campaigns');
  const [selectedItem, setSelectedItem] = useState<{ id: string; name: string; type: string } | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'performance' | 'spend'>('name');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setIsRefreshing(false);
    }, 1500);
  };

  const handleViewDetails = (id: string, name: string, type: string) => {
    setSelectedItem({ id, name, type });
    setShowDetailModal(true);
  };

  const getFilteredItems = () => {
    let items: Array<{ id: string; name: string; type: string }> = [];
    
    switch (activeItemType) {
      case 'campaigns':
        items = campaigns.map(c => ({ id: c.id, name: c.name, type: 'campaign' }));
        break;
      case 'adsets':
        items = adSets.map(a => ({ id: a.id, name: a.name, type: 'adset' }));
        break;
      case 'ads':
        items = ads.map(a => ({ id: a.id, name: a.name, type: 'ad' }));
        break;
    }

    if (searchTerm) {
      items = items.filter(item => 
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return items;
  };

  const getItemTypeLabel = (type: ItemType) => {
    const labels = {
      campaigns: 'Chiến dịch',
      adsets: 'Nhóm quảng cáo',
      ads: 'Quảng cáo'
    };
    return labels[type];
  };

  const filteredItems = getFilteredItems();

  if (filteredItems.length === 0 && !searchTerm) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <BarChart3 className="w-10 h-10 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">Không có dữ liệu hiệu suất</h3>
        <p className="text-gray-600">
          Không có {getItemTypeLabel(activeItemType).toLowerCase()} nào để hiển thị.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="bg-white rounded-xl border border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          {/* Left side - Type selector and search */}
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              <h2 className="text-lg font-semibold text-gray-900">Chi tiết hiệu suất</h2>
            </div>
            
            <div className="flex items-center space-x-2">
              {(['campaigns', 'adsets', 'ads'] as ItemType[]).map((type) => (
                <button
                  key={type}
                  onClick={() => setActiveItemType(type)}
                  className={`px-3 py-1.5 text-sm font-medium rounded-lg transition-colors ${
                    activeItemType === type
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {getItemTypeLabel(type)} ({
                    type === 'campaigns' ? campaigns.length :
                    type === 'adsets' ? adSets.length :
                    ads.length
                  })
                </button>
              ))}
            </div>
          </div>

          {/* Right side - Controls */}
          <div className="flex items-center space-x-3">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tìm kiếm..."
                className="pl-8 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent w-48"
              />
              <div className="absolute left-2.5 top-1/2 transform -translate-y-1/2">
                <Search className="w-3 h-3 text-gray-400" />
              </div>
            </div>

            {/* Sort */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="name">Tên</option>
              <option value="performance">Hiệu suất</option>
              <option value="spend">Chi phí</option>
            </select>

            {/* View Mode */}
            <div className="flex items-center bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-1.5 rounded ${viewMode === 'grid' ? 'bg-white shadow-sm' : ''}`}
                title="Xem dạng lưới"
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-1.5 rounded ${viewMode === 'list' ? 'bg-white shadow-sm' : ''}`}
                title="Xem dạng danh sách"
              >
                <List className="w-4 h-4" />
              </button>
            </div>

            {/* Refresh */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              title="Làm mới dữ liệu"
            >
              <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>

            {/* Export */}
            <button
              className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
              title="Xuất dữ liệu"
            >
              <Download className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      {filteredItems.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-4">
            <Target className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Không tìm thấy kết quả</h3>
          <p className="text-gray-600">
            Không có {getItemTypeLabel(activeItemType).toLowerCase()} nào khớp với từ khóa "{searchTerm}"
          </p>
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredItems.map((item) => (
                <div key={item.id} className="relative group">
                  <PerformanceSummaryCard
                    itemId={item.id}
                    itemName={item.name}
                    itemType={item.type as any}
                    accountId={accountId}
                    className="cursor-pointer"
                  />
                  
                  {/* Overlay with action button */}
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 rounded-lg transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <button
                      onClick={() => handleViewDetails(item.id, item.name, item.type)}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-lg"
                    >
                      Xem chi tiết
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tên
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hiệu suất
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Hành động
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredItems.map((item) => (
                      <tr key={item.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{item.name}</div>
                            <div className="text-sm text-gray-500 font-mono">{item.id}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <PerformanceSummaryCard
                            itemId={item.id}
                            itemName={item.name}
                            itemType={item.type as any}
                            accountId={accountId}
                            className="max-w-xs"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <button
                            onClick={() => handleViewDetails(item.id, item.name, item.type)}
                            className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors"
                          >
                            <BarChart3 className="w-3 h-3 mr-1" />
                            Chi tiết
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </>
      )}

      {/* Detailed Insights Modal */}
      {selectedItem && (
        <DetailedInsightsModal
          isOpen={showDetailModal}
          onClose={() => {
            setShowDetailModal(false);
            setSelectedItem(null);
          }}
          itemId={selectedItem.id}
          itemName={selectedItem.name}
          itemType={selectedItem.type as any}
          accountId={accountId}
        />
      )}
    </div>
  );
};
