# Facebook Ads Dashboard

A comprehensive React application for retrieving and visualizing Facebook Ads data through the Facebook Marketing API.

## Features

- **Facebook Authentication**: Secure login with Facebook SDK
- **Multi-Account Support**: Switch between different ad accounts
- **Campaign Management**: View and analyze campaigns, ad sets, and ads
- **Performance Insights**: Detailed metrics and performance data
- **Data Export**: Export data in JSON format
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Refresh data with latest information

## Setup Instructions

### 1. Facebook App Configuration

1. Visit [Facebook Developers](https://developers.facebook.com/)
2. Create a new app or use an existing one
3. Add the "Facebook Login" product to your app
4. Configure OAuth redirect URIs to include your domain
5. Add the "Marketing API" product for ads access
6. Get your App ID from the app dashboard

### 2. Required Permissions

The app requests the following Facebook permissions:
- `ads_read`: Read ads data
- `ads_management`: Manage ads
- `business_management`: Access business data
- `pages_read_engagement`: Read page engagement data

### 3. Environment Setup

No environment variables are required as the Facebook App ID is entered directly in the login form.

### 4. Local Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### 5. Production Deployment

```bash
# Build for production
npm run build

# Preview production build
npm run preview
```

## Usage

1. **Login**: Enter your Facebook App ID and click "Connect with Facebook"
2. **Authorize**: Grant the required permissions to access your ads data
3. **Select Account**: Choose which ad account to analyze from the dropdown
4. **Choose Date Range**: Select the time period for your data analysis
5. **Navigate Tabs**: Switch between Campaigns, Ad Sets, Ads, and Insights
6. **Export Data**: Click the download button to export data as JSON

## Data Structure

### Campaigns
- Campaign name, status, and objective
- Budget information (daily/lifetime)
- Creation and scheduling details

### Ad Sets
- Ad set name, status, and optimization settings
- Budget allocation and bid strategy
- Targeting and scheduling information

### Ads
- Ad name, status, and creative information
- Associated ad set and campaign IDs
- Creation and update timestamps

### Insights
- Performance metrics (impressions, clicks, spend)
- Conversion data and action types
- Cost metrics (CPM, CPC, CTR)
- Reach and frequency data

## API Integration

The application uses the Facebook JavaScript SDK to interact with the Marketing API:

- **Authentication**: Facebook Login with proper scope permissions
- **Data Fetching**: Real-time API calls to fetch ads data
- **Error Handling**: Comprehensive error handling for API failures
- **Rate Limiting**: Respects Facebook API rate limits

## Security Considerations

- Uses Facebook's official JavaScript SDK
- Access tokens are managed securely in memory
- No sensitive data is stored locally
- Proper permission scoping for minimal access

## Browser Compatibility

- Modern browsers with ES6+ support
- Facebook SDK compatibility
- WebContainer environment optimized

## Troubleshooting

### Common Issues

1. **App ID Invalid**: Ensure your Facebook App ID is correct
2. **Permission Denied**: Check that required permissions are granted
3. **No Ad Accounts**: Verify that your Facebook account has access to ad accounts
4. **API Errors**: Check Facebook Developer Console for app status

### Debug Mode

The application includes comprehensive error messages and loading states to help identify issues.

## Development Notes

- Built with React 18 and TypeScript
- Uses Tailwind CSS for styling
- Lucide React for icons
- Modular component architecture
- Comprehensive type definitions for Facebook API responses

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is for educational and development purposes. Ensure compliance with Facebook's Platform Terms and Marketing API policies.