import { MongoClient, Db, Collection } from 'mongodb';

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017';
const DATABASE_NAME = process.env.DATABASE_NAME || 'facebook_ads_dashboard';

// Data interfaces for MongoDB
export interface CampaignInsights {
  _id?: string;
  campaign_id: string;
  campaign_name: string;
  account_id: string;
  date: Date;
  impressions: number;
  clicks: number;
  spend: number;
  reach: number;
  frequency: number;
  ctr: number;
  cpc: number;
  cpm: number;
  actions?: Array<{
    action_type: string;
    value: number;
  }>;
  cost_per_action_type?: Array<{
    action_type: string;
    value: number;
  }>;
  created_at: Date;
  updated_at: Date;
}

export interface AdSetInsights {
  _id?: string;
  adset_id: string;
  adset_name: string;
  campaign_id: string;
  account_id: string;
  date: Date;
  impressions: number;
  clicks: number;
  spend: number;
  reach: number;
  frequency: number;
  ctr: number;
  cpc: number;
  cpm: number;
  actions?: Array<{
    action_type: string;
    value: number;
  }>;
  created_at: Date;
  updated_at: Date;
}

export interface AdInsights {
  _id?: string;
  ad_id: string;
  ad_name: string;
  adset_id: string;
  campaign_id: string;
  account_id: string;
  date: Date;
  impressions: number;
  clicks: number;
  spend: number;
  reach: number;
  frequency: number;
  ctr: number;
  cpc: number;
  cpm: number;
  actions?: Array<{
    action_type: string;
    value: number;
  }>;
  creative?: {
    title: string;
    body: string;
    image_url?: string;
    video_url?: string;
  };
  created_at: Date;
  updated_at: Date;
}

export interface SyncLog {
  _id?: string;
  account_id: string;
  sync_type: 'campaigns' | 'adsets' | 'ads';
  status: 'success' | 'error' | 'in_progress';
  records_synced: number;
  error_message?: string;
  started_at: Date;
  completed_at?: Date;
}

class MongoDbService {
  private client: MongoClient | null = null;
  private db: Db | null = null;

  async connect(): Promise<void> {
    try {
      this.client = new MongoClient(MONGODB_URI);
      await this.client.connect();
      this.db = this.client.db(DATABASE_NAME);
      console.log('✅ Connected to MongoDB successfully');
    } catch (error) {
      console.error('❌ MongoDB connection error:', error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.client = null;
      this.db = null;
      console.log('✅ Disconnected from MongoDB');
    }
  }

  private getCollection<T>(collectionName: string): Collection<T> {
    if (!this.db) {
      throw new Error('Database not connected');
    }
    return this.db.collection<T>(collectionName);
  }

  // Campaign Insights Methods
  async saveCampaignInsights(insights: CampaignInsights[]): Promise<void> {
    const collection = this.getCollection<CampaignInsights>('campaign_insights');
    
    const operations = insights.map(insight => ({
      updateOne: {
        filter: { 
          campaign_id: insight.campaign_id, 
          date: insight.date 
        },
        update: { 
          $set: { 
            ...insight, 
            updated_at: new Date() 
          } 
        },
        upsert: true
      }
    }));

    await collection.bulkWrite(operations);
    console.log(`✅ Saved ${insights.length} campaign insights`);
  }

  async getCampaignInsights(
    accountId: string, 
    dateRange: { start: Date; end: Date },
    campaignIds?: string[]
  ): Promise<CampaignInsights[]> {
    const collection = this.getCollection<CampaignInsights>('campaign_insights');
    
    const filter: any = {
      account_id: accountId,
      date: { $gte: dateRange.start, $lte: dateRange.end }
    };

    if (campaignIds && campaignIds.length > 0) {
      filter.campaign_id = { $in: campaignIds };
    }

    return await collection.find(filter).sort({ date: -1 }).toArray();
  }

  // AdSet Insights Methods
  async saveAdSetInsights(insights: AdSetInsights[]): Promise<void> {
    const collection = this.getCollection<AdSetInsights>('adset_insights');
    
    const operations = insights.map(insight => ({
      updateOne: {
        filter: { 
          adset_id: insight.adset_id, 
          date: insight.date 
        },
        update: { 
          $set: { 
            ...insight, 
            updated_at: new Date() 
          } 
        },
        upsert: true
      }
    }));

    await collection.bulkWrite(operations);
    console.log(`✅ Saved ${insights.length} adset insights`);
  }

  async getAdSetInsights(
    accountId: string, 
    dateRange: { start: Date; end: Date },
    adsetIds?: string[]
  ): Promise<AdSetInsights[]> {
    const collection = this.getCollection<AdSetInsights>('adset_insights');
    
    const filter: any = {
      account_id: accountId,
      date: { $gte: dateRange.start, $lte: dateRange.end }
    };

    if (adsetIds && adsetIds.length > 0) {
      filter.adset_id = { $in: adsetIds };
    }

    return await collection.find(filter).sort({ date: -1 }).toArray();
  }

  // Ad Insights Methods
  async saveAdInsights(insights: AdInsights[]): Promise<void> {
    const collection = this.getCollection<AdInsights>('ad_insights');
    
    const operations = insights.map(insight => ({
      updateOne: {
        filter: { 
          ad_id: insight.ad_id, 
          date: insight.date 
        },
        update: { 
          $set: { 
            ...insight, 
            updated_at: new Date() 
          } 
        },
        upsert: true
      }
    }));

    await collection.bulkWrite(operations);
    console.log(`✅ Saved ${insights.length} ad insights`);
  }

  async getAdInsights(
    accountId: string, 
    dateRange: { start: Date; end: Date },
    adIds?: string[]
  ): Promise<AdInsights[]> {
    const collection = this.getCollection<AdInsights>('ad_insights');
    
    const filter: any = {
      account_id: accountId,
      date: { $gte: dateRange.start, $lte: dateRange.end }
    };

    if (adIds && adIds.length > 0) {
      filter.ad_id = { $in: adIds };
    }

    return await collection.find(filter).sort({ date: -1 }).toArray();
  }

  // Aggregation Methods
  async getPerformanceSummary(
    accountId: string, 
    itemType: 'campaign' | 'adset' | 'ad',
    itemId: string,
    dateRange: { start: Date; end: Date }
  ): Promise<any> {
    const collectionName = `${itemType}_insights`;
    const collection = this.getCollection(collectionName);
    const idField = `${itemType}_id`;

    const pipeline = [
      {
        $match: {
          account_id: accountId,
          [idField]: itemId,
          date: { $gte: dateRange.start, $lte: dateRange.end }
        }
      },
      {
        $group: {
          _id: null,
          total_impressions: { $sum: '$impressions' },
          total_clicks: { $sum: '$clicks' },
          total_spend: { $sum: '$spend' },
          total_reach: { $sum: '$reach' },
          avg_frequency: { $avg: '$frequency' },
          avg_ctr: { $avg: '$ctr' },
          avg_cpc: { $avg: '$cpc' },
          avg_cpm: { $avg: '$cpm' },
          days_count: { $sum: 1 }
        }
      }
    ];

    const result = await collection.aggregate(pipeline).toArray();
    return result[0] || null;
  }

  // Sync Log Methods
  async logSyncStart(accountId: string, syncType: 'campaigns' | 'adsets' | 'ads'): Promise<string> {
    const collection = this.getCollection<SyncLog>('sync_logs');
    
    const log: SyncLog = {
      account_id: accountId,
      sync_type: syncType,
      status: 'in_progress',
      records_synced: 0,
      started_at: new Date()
    };

    const result = await collection.insertOne(log);
    return result.insertedId.toString();
  }

  async logSyncComplete(
    logId: string, 
    recordsSynced: number, 
    errorMessage?: string
  ): Promise<void> {
    const collection = this.getCollection<SyncLog>('sync_logs');
    
    await collection.updateOne(
      { _id: logId as any },
      {
        $set: {
          status: errorMessage ? 'error' : 'success',
          records_synced: recordsSynced,
          error_message: errorMessage,
          completed_at: new Date()
        }
      }
    );
  }

  async getLastSyncTime(accountId: string, syncType: 'campaigns' | 'adsets' | 'ads'): Promise<Date | null> {
    const collection = this.getCollection<SyncLog>('sync_logs');
    
    const lastSync = await collection.findOne(
      { 
        account_id: accountId, 
        sync_type: syncType, 
        status: 'success' 
      },
      { sort: { completed_at: -1 } }
    );

    return lastSync?.completed_at || null;
  }

  // Utility Methods
  async createIndexes(): Promise<void> {
    if (!this.db) return;

    // Campaign insights indexes
    await this.db.collection('campaign_insights').createIndexes([
      { key: { campaign_id: 1, date: 1 }, unique: true },
      { key: { account_id: 1, date: -1 } },
      { key: { date: -1 } }
    ]);

    // AdSet insights indexes
    await this.db.collection('adset_insights').createIndexes([
      { key: { adset_id: 1, date: 1 }, unique: true },
      { key: { account_id: 1, date: -1 } },
      { key: { campaign_id: 1, date: -1 } }
    ]);

    // Ad insights indexes
    await this.db.collection('ad_insights').createIndexes([
      { key: { ad_id: 1, date: 1 }, unique: true },
      { key: { account_id: 1, date: -1 } },
      { key: { adset_id: 1, date: -1 } },
      { key: { campaign_id: 1, date: -1 } }
    ]);

    // Sync logs indexes
    await this.db.collection('sync_logs').createIndexes([
      { key: { account_id: 1, sync_type: 1, completed_at: -1 } },
      { key: { started_at: -1 } }
    ]);

    console.log('✅ Created MongoDB indexes');
  }

  async getHealthCheck(): Promise<{ status: string; collections: any }> {
    if (!this.db) {
      return { status: 'disconnected', collections: {} };
    }

    const collections = await this.db.listCollections().toArray();
    const stats = {};

    for (const collection of collections) {
      const count = await this.db.collection(collection.name).countDocuments();
      stats[collection.name] = count;
    }

    return { status: 'connected', collections: stats };
  }
}

export const mongoDbService = new MongoDbService();
