#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { createCronSyncService, defaultSyncConfig } from '../src/services/cronSyncService';
import { mongoApiService } from '../src/utils/mongoApiService';
import { databaseConfig, validateEnvironment, checkDatabaseHealth } from '../src/config/database';

const app = express();
const PORT = process.env.CRON_SERVER_PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Global variables
let cronService: any = null;
let isInitialized = false;

// Initialize cron service
const initializeCronService = async () => {
  if (isInitialized) return;
  
  try {
    console.log('🚀 Initializing Cron Sync Service...');
    
    // Validate environment
    validateEnvironment();
    
    // Check database health
    const dbHealth = await checkDatabaseHealth();
    if (dbHealth.status !== 'healthy') {
      throw new Error(`Database unhealthy: ${dbHealth.error}`);
    }
    
    // Create cron service with configuration
    const config = {
      ...defaultSyncConfig,
      accountIds: (process.env.FACEBOOK_ACCOUNT_IDS || '').split(',').filter(Boolean),
      accessToken: process.env.FACEBOOK_ACCESS_TOKEN || '',
      syncIntervalHours: databaseConfig.sync.intervalHours,
      maxDaysHistory: databaseConfig.sync.maxDaysHistory,
      enabledSyncTypes: databaseConfig.sync.enabledTypes as any
    };
    
    if (config.accountIds.length === 0) {
      console.warn('⚠️ No Facebook account IDs configured. Cron service will not sync data.');
    }
    
    cronService = createCronSyncService(config);
    
    // Start cron service
    await cronService.start();
    
    isInitialized = true;
    console.log('✅ Cron Sync Service initialized successfully');
    
  } catch (error) {
    console.error('❌ Failed to initialize Cron Sync Service:', error);
    throw error;
  }
};

// API Routes

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await checkDatabaseHealth();
    const cronHealth = cronService ? await cronService.getHealthStatus() : { status: 'not_initialized' };
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      services: {
        database: dbHealth,
        cron_service: cronHealth,
        api_service: await mongoApiService.getHealthStatus()
      }
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Manual sync endpoint
app.post('/sync/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    
    if (!cronService) {
      return res.status(503).json({
        error: 'Cron service not initialized'
      });
    }
    
    console.log(`🔄 Manual sync requested for account: ${accountId}`);
    await cronService.manualSync(accountId);
    
    res.json({
      status: 'success',
      message: `Manual sync completed for account ${accountId}`,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Manual sync failed:', error);
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get sync status
app.get('/sync/status/:accountId', async (req, res) => {
  try {
    const { accountId } = req.params;
    
    if (!cronService) {
      return res.status(503).json({
        error: 'Cron service not initialized'
      });
    }
    
    const status = await cronService.getLastSyncStatus(accountId);
    res.json(status);
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get account overview from MongoDB
app.get('/api/accounts/:accountId/overview', async (req, res) => {
  try {
    const { accountId } = req.params;
    const { datePreset = 'last_7d' } = req.query;
    
    const overview = await mongoApiService.getAccountOverview(accountId, datePreset as string);
    res.json(overview);
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get campaigns from MongoDB
app.get('/api/accounts/:accountId/campaigns', async (req, res) => {
  try {
    const { accountId } = req.params;
    const campaigns = await mongoApiService.getCampaigns(accountId);
    res.json({ data: campaigns });
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get ad sets from MongoDB
app.get('/api/accounts/:accountId/adsets', async (req, res) => {
  try {
    const { accountId } = req.params;
    const adSets = await mongoApiService.getAdSets(accountId);
    res.json({ data: adSets });
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get ads from MongoDB
app.get('/api/accounts/:accountId/ads', async (req, res) => {
  try {
    const { accountId } = req.params;
    const ads = await mongoApiService.getAds(accountId);
    res.json({ data: ads });
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get insights from MongoDB
app.get('/api/:itemId/insights', async (req, res) => {
  try {
    const { itemId } = req.params;
    const { level = 'ad', date_preset = 'last_7d' } = req.query;
    
    const insights = await mongoApiService.getInsights(
      itemId, 
      level as any, 
      date_preset as string
    );
    
    res.json({ data: insights });
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get performance summary
app.get('/api/:itemId/performance', async (req, res) => {
  try {
    const { itemId } = req.params;
    const { accountId, itemType = 'ad', datePreset = 'last_7d' } = req.query;
    
    if (!accountId) {
      return res.status(400).json({
        error: 'accountId query parameter is required'
      });
    }
    
    const summary = await mongoApiService.getPerformanceSummary(
      accountId as string,
      itemType as any,
      itemId,
      datePreset as string
    );
    
    res.json(summary);
    
  } catch (error) {
    res.status(500).json({
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Graceful shutdown
const gracefulShutdown = async () => {
  console.log('🛑 Shutting down Cron Server...');
  
  if (cronService) {
    await cronService.stop();
  }
  
  process.exit(0);
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
const startServer = async () => {
  try {
    // Initialize cron service
    await initializeCronService();
    
    // Start Express server
    app.listen(PORT, () => {
      console.log(`🚀 Cron Server running on port ${PORT}`);
      console.log(`📊 Health check: http://localhost:${PORT}/health`);
      console.log(`🔄 Manual sync: POST http://localhost:${PORT}/sync/:accountId`);
    });
    
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
};

// Start the server
startServer();
