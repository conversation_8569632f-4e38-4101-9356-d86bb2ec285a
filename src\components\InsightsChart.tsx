import React from 'react';
import { Insights } from '../types/facebook';
import { Bar<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, MousePointer, DollarSign } from 'lucide-react';

interface InsightsChartProps {
  insights: Insights[];
}

export const InsightsChart: React.FC<InsightsChartProps> = ({ insights }) => {
  if (insights.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <BarChart3 className="w-10 h-10 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">Không có dữ liệu báo cáo</h3>
        <p className="text-gray-600">
          Không có dữ liệu báo cáo cho khoảng thời gian đã chọn.
        </p>
      </div>
    );
  }

  // Calculate metrics for each insight period
  const processedInsights = insights.map(insight => ({
    ...insight,
    ctr: parseInt(insight.impressions) > 0 
      ? ((parseInt(insight.clicks) / parseInt(insight.impressions)) * 100).toFixed(2)
      : '0.00',
    spend_formatted: parseFloat(insight.spend).toLocaleString('vi-VN')
  }));

  return (
    <div className="space-y-8">
      {/* Summary Table */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 shadow-lg">
        <div className="px-8 py-6 border-b border-gray-200/50">
          <h3 className="text-xl font-bold text-gray-900 flex items-center">
            <BarChart3 className="w-6 h-6 mr-3 text-blue-600" />
            Báo cáo hiệu suất
          </h3>
        </div>
        <div className="overflow-x-auto rounded-b-2xl">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Khoảng thời gian
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>Lượt hiển thị</span>
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center space-x-1">
                    <MousePointer className="w-4 h-4" />
                    <span>Lượt nhấp</span>
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center space-x-1">
                    <DollarSign className="w-4 h-4" />
                    <span>Chi phí</span>
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  <div className="flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4" />
                    <span>CTR</span>
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  Tiếp cận
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  CPM
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                  CPC
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {processedInsights.map((insight, index) => (
                <tr key={index} className="hover:bg-blue-50/50 transition-colors">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">
                    {insight.date_start === insight.date_stop 
                      ? insight.date_start 
                      : `${insight.date_start} - ${insight.date_stop}`
                    }
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {parseInt(insight.impressions).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {parseInt(insight.clicks).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                    {insight.spend_formatted} ₫
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                    {insight.ctr}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {parseInt(insight.reach).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {parseFloat(insight.cpm).toLocaleString('vi-VN')} ₫
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {parseFloat(insight.cpc).toLocaleString('vi-VN')} ₫
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Actions Data */}
      {insights.some(insight => insight.actions && insight.actions.length > 0) && (
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-200/50 shadow-lg">
          <div className="px-8 py-6 border-b border-gray-200/50">
            <h3 className="text-xl font-bold text-gray-900">Hành động & Chuyển đổi</h3>
          </div>
          <div className="p-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {insights.map((insight, insightIndex) => 
                insight.actions?.map((action, actionIndex) => (
                  <div key={`${insightIndex}-${actionIndex}`} className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100 hover:shadow-md transition-all duration-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-bold text-blue-900 capitalize mb-2">
                          {action.action_type === 'purchase' ? 'Mua hàng' :
                           action.action_type === 'add_to_cart' ? 'Thêm vào giỏ' :
                           action.action_type === 'view_content' ? 'Xem nội dung' :
                           action.action_type === 'initiate_checkout' ? 'Bắt đầu thanh toán' :
                           action.action_type.replace(/_/g, ' ')}
                        </p>
                        <p className="text-3xl font-bold text-blue-600">{action.value}</p>
                      </div>
                      <div className="text-xs text-blue-600 font-medium">
                        {insight.date_start}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};