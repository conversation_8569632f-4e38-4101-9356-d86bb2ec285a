import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  Eye, 
  MousePointer, 
  DollarSign, 
  Users, 
  Target,
  Activity,
  Award,
  AlertTriangle,
  CheckCircle,
  Minus
} from 'lucide-react';
import { facebookApi } from '../utils/facebookApi';
import { Insights } from '../types/facebook';

interface PerformanceSummaryCardProps {
  itemId: string;
  itemName: string;
  itemType: 'campaign' | 'adset' | 'ad';
  accountId: string;
  className?: string;
}

interface PerformanceMetrics {
  impressions: number;
  clicks: number;
  spend: number;
  reach: number;
  ctr: number;
  cpc: number;
  cpm: number;
  conversions: number;
  roas: number;
  performance: 'excellent' | 'good' | 'average' | 'poor';
}

export const PerformanceSummaryCard: React.FC<PerformanceSummaryCardProps> = ({
  itemId,
  itemName,
  itemType,
  accountId,
  className = ''
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPerformanceMetrics();
  }, [itemId]);

  const loadPerformanceMetrics = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const insights = await facebookApi.getInsights(itemId, 'ad', 'last_7d');
      
      if (insights.length === 0) {
        setMetrics(null);
        return;
      }

      // Calculate aggregated metrics
      const aggregated = insights.reduce((acc, insight) => {
        acc.impressions += parseInt(insight.impressions) || 0;
        acc.clicks += parseInt(insight.clicks) || 0;
        acc.spend += parseFloat(insight.spend) || 0;
        acc.reach += parseInt(insight.reach) || 0;
        
        // Calculate conversions from actions
        if (insight.actions) {
          const purchases = insight.actions.find(a => a.action_type === 'purchase');
          if (purchases) {
            acc.conversions += parseInt(purchases.value) || 0;
          }
        }
        
        return acc;
      }, {
        impressions: 0,
        clicks: 0,
        spend: 0,
        reach: 0,
        conversions: 0
      });

      // Calculate derived metrics
      const ctr = aggregated.impressions > 0 ? (aggregated.clicks / aggregated.impressions) * 100 : 0;
      const cpc = aggregated.clicks > 0 ? aggregated.spend / aggregated.clicks : 0;
      const cpm = aggregated.impressions > 0 ? (aggregated.spend / aggregated.impressions) * 1000 : 0;
      const roas = aggregated.conversions > 0 ? (aggregated.conversions * 500000) / aggregated.spend : 0; // Assuming 500k VND per conversion

      // Determine performance level
      let performance: 'excellent' | 'good' | 'average' | 'poor' = 'poor';
      
      if (ctr >= 3.0 && cpc <= 8000 && cpm <= 40000) {
        performance = 'excellent';
      } else if (ctr >= 2.0 && cpc <= 12000 && cpm <= 60000) {
        performance = 'good';
      } else if (ctr >= 1.0 && cpc <= 20000 && cpm <= 80000) {
        performance = 'average';
      }

      setMetrics({
        ...aggregated,
        ctr,
        cpc,
        cpm,
        roas,
        performance
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể tải dữ liệu hiệu suất');
    } finally {
      setIsLoading(false);
    }
  };

  const getPerformanceIcon = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return <Award className="w-5 h-5 text-green-600" />;
      case 'good':
        return <CheckCircle className="w-5 h-5 text-blue-600" />;
      case 'average':
        return <Minus className="w-5 h-5 text-yellow-600" />;
      default:
        return <AlertTriangle className="w-5 h-5 text-red-600" />;
    }
  };

  const getPerformanceColor = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'good':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'average':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      default:
        return 'bg-red-50 border-red-200 text-red-800';
    }
  };

  const getPerformanceLabel = (performance: string) => {
    switch (performance) {
      case 'excellent':
        return 'Xuất sắc';
      case 'good':
        return 'Tốt';
      case 'average':
        return 'Trung bình';
      default:
        return 'Cần cải thiện';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value);
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="grid grid-cols-2 gap-2">
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
            <div className="h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-4 h-4 text-red-600 flex-shrink-0" />
          <span className="text-red-800 text-sm">{error}</span>
        </div>
      </div>
    );
  }

  if (!metrics) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-4 ${className}`}>
        <div className="text-center">
          <Activity className="w-8 h-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-600 text-sm">Không có dữ liệu hiệu suất</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div>
          <h4 className="font-medium text-gray-900 truncate">{itemName}</h4>
          <p className="text-xs text-gray-500">7 ngày qua</p>
        </div>
        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border ${getPerformanceColor(metrics.performance)}`}>
          {getPerformanceIcon(metrics.performance)}
          <span className="text-xs font-medium">{getPerformanceLabel(metrics.performance)}</span>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-2 gap-3 mb-3">
        <div className="bg-blue-50 rounded-lg p-2">
          <div className="flex items-center justify-between">
            <Eye className="w-4 h-4 text-blue-600" />
            <div className="text-right">
              <p className="text-xs text-blue-600 font-medium">Hiển thị</p>
              <p className="text-sm font-bold text-blue-900">{metrics.impressions.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-green-50 rounded-lg p-2">
          <div className="flex items-center justify-between">
            <MousePointer className="w-4 h-4 text-green-600" />
            <div className="text-right">
              <p className="text-xs text-green-600 font-medium">Nhấp</p>
              <p className="text-sm font-bold text-green-900">{metrics.clicks.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-2">
          <div className="flex items-center justify-between">
            <DollarSign className="w-4 h-4 text-purple-600" />
            <div className="text-right">
              <p className="text-xs text-purple-600 font-medium">Chi phí</p>
              <p className="text-sm font-bold text-purple-900">{formatCurrency(metrics.spend)}</p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 rounded-lg p-2">
          <div className="flex items-center justify-between">
            <Target className="w-4 h-4 text-orange-600" />
            <div className="text-right">
              <p className="text-xs text-orange-600 font-medium">CTR</p>
              <p className="text-sm font-bold text-orange-900">{metrics.ctr.toFixed(2)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Performance Indicators */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600">CPC</span>
          <div className="flex items-center space-x-1">
            <span className="font-medium">{formatCurrency(metrics.cpc)}</span>
            {metrics.cpc <= 10000 ? (
              <TrendingUp className="w-3 h-3 text-green-600" />
            ) : (
              <TrendingDown className="w-3 h-3 text-red-600" />
            )}
          </div>
        </div>

        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-600">CPM</span>
          <div className="flex items-center space-x-1">
            <span className="font-medium">{formatCurrency(metrics.cpm)}</span>
            {metrics.cpm <= 50000 ? (
              <TrendingUp className="w-3 h-3 text-green-600" />
            ) : (
              <TrendingDown className="w-3 h-3 text-red-600" />
            )}
          </div>
        </div>

        {metrics.conversions > 0 && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">Chuyển đổi</span>
            <div className="flex items-center space-x-1">
              <span className="font-medium">{metrics.conversions}</span>
              <Award className="w-3 h-3 text-yellow-600" />
            </div>
          </div>
        )}

        {metrics.roas > 0 && (
          <div className="flex items-center justify-between text-xs">
            <span className="text-gray-600">ROAS</span>
            <div className="flex items-center space-x-1">
              <span className="font-medium">{metrics.roas.toFixed(2)}x</span>
              {metrics.roas >= 3.0 ? (
                <TrendingUp className="w-3 h-3 text-green-600" />
              ) : (
                <TrendingDown className="w-3 h-3 text-red-600" />
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
