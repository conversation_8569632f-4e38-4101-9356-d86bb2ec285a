import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Basic test without MongoDB
async function testBasic() {
  console.log('🔍 Running Basic Tests...\n');
  
  // Test 1: Environment Variables
  console.log('📋 Test 1: Environment Variables');
  console.log('================================');
  
  const requiredEnvs = [
    'MONGODB_URI',
    'DATABASE_NAME',
    'SYNC_INTERVAL_HOURS',
    'CRON_SERVER_PORT'
  ];
  
  let envTestPassed = true;
  
  for (const env of requiredEnvs) {
    const value = process.env[env];
    if (value) {
      console.log(`✅ ${env}: ${value}`);
    } else {
      console.log(`❌ ${env}: Not set`);
      envTestPassed = false;
    }
  }
  
  console.log(`\n📊 Environment Test: ${envTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  // Test 2: Import Tests
  console.log('📋 Test 2: Module Imports');
  console.log('=========================');
  
  let importTestPassed = true;
  
  try {
    const { mongoDbService } = await import('../src/utils/mongoDbService');
    console.log('✅ mongoDbService imported successfully');
  } catch (error) {
    console.log('❌ mongoDbService import failed:', error.message);
    importTestPassed = false;
  }
  
  try {
    const { createCronSyncService } = await import('../src/services/cronSyncService');
    console.log('✅ cronSyncService imported successfully');
  } catch (error) {
    console.log('❌ cronSyncService import failed:', error.message);
    importTestPassed = false;
  }
  
  try {
    const { mongoApiService } = await import('../src/utils/mongoApiService');
    console.log('✅ mongoApiService imported successfully');
  } catch (error) {
    console.log('❌ mongoApiService import failed:', error.message);
    importTestPassed = false;
  }
  
  console.log(`\n📊 Import Test: ${importTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  // Test 3: Configuration Test
  console.log('📋 Test 3: Configuration Validation');
  console.log('===================================');
  
  let configTestPassed = true;
  
  try {
    const { databaseConfig } = await import('../src/config/database');
    console.log('✅ Database config loaded successfully');
    console.log(`   - MongoDB URI: ${databaseConfig.mongodb.uri}`);
    console.log(`   - Database Name: ${databaseConfig.mongodb.database}`);
    console.log(`   - Sync Interval: ${databaseConfig.sync.intervalHours} hours`);
    console.log(`   - Max Days History: ${databaseConfig.sync.maxDaysHistory} days`);
  } catch (error) {
    console.log('❌ Database config failed:', error.message);
    configTestPassed = false;
  }
  
  console.log(`\n📊 Configuration Test: ${configTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  // Test 4: Mock Data Generation Test
  console.log('📋 Test 4: Mock Data Generation');
  console.log('===============================');
  
  let mockDataTestPassed = true;
  
  try {
    // Test mock data structure
    const mockCampaignInsight = {
      campaign_id: 'test_campaign_123',
      campaign_name: 'Test Campaign',
      account_id: 'act_123456789',
      date: new Date(),
      impressions: 1000,
      clicks: 50,
      spend: 100000,
      reach: 800,
      frequency: 1.25,
      ctr: 5.0,
      cpc: 2000,
      cpm: 100000,
      actions: [
        { action_type: 'purchase', value: 5 },
        { action_type: 'add_to_cart', value: 20 }
      ],
      created_at: new Date(),
      updated_at: new Date()
    };
    
    console.log('✅ Mock campaign insight structure valid');
    console.log(`   - Campaign ID: ${mockCampaignInsight.campaign_id}`);
    console.log(`   - Impressions: ${mockCampaignInsight.impressions.toLocaleString()}`);
    console.log(`   - CTR: ${mockCampaignInsight.ctr}%`);
    console.log(`   - CPC: ${mockCampaignInsight.cpc.toLocaleString()} VND`);
    console.log(`   - Actions: ${mockCampaignInsight.actions.length} types`);
    
    // Test performance calculation
    const performance = mockCampaignInsight.ctr >= 3.0 && 
                       mockCampaignInsight.cpc <= 8000 ? 'excellent' :
                       mockCampaignInsight.ctr >= 2.0 && 
                       mockCampaignInsight.cpc <= 12000 ? 'good' : 'average';
    
    console.log(`   - Performance Level: ${performance}`);
    
  } catch (error) {
    console.log('❌ Mock data generation failed:', error.message);
    mockDataTestPassed = false;
  }
  
  console.log(`\n📊 Mock Data Test: ${mockDataTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  // Test 5: API Endpoint Structure Test
  console.log('📋 Test 5: API Endpoint Structure');
  console.log('=================================');
  
  let apiTestPassed = true;
  
  try {
    // Test API endpoint definitions
    const endpoints = [
      'GET /health',
      'POST /sync/:accountId',
      'GET /sync/status/:accountId',
      'GET /api/accounts/:accountId/overview',
      'GET /api/accounts/:accountId/campaigns',
      'GET /api/accounts/:accountId/adsets',
      'GET /api/accounts/:accountId/ads',
      'GET /api/:itemId/insights',
      'GET /api/:itemId/performance'
    ];
    
    console.log('✅ API endpoints defined:');
    endpoints.forEach(endpoint => {
      console.log(`   - ${endpoint}`);
    });
    
    // Test server configuration
    const serverPort = process.env.CRON_SERVER_PORT || 3001;
    const apiBaseUrl = process.env.VITE_API_BASE_URL || `http://localhost:${serverPort}`;
    
    console.log(`✅ Server configuration:`);
    console.log(`   - Port: ${serverPort}`);
    console.log(`   - Base URL: ${apiBaseUrl}`);
    
  } catch (error) {
    console.log('❌ API structure test failed:', error.message);
    apiTestPassed = false;
  }
  
  console.log(`\n📊 API Structure Test: ${apiTestPassed ? '✅ PASSED' : '❌ FAILED'}\n`);
  
  // Final Results
  console.log('🎯 FINAL TEST RESULTS');
  console.log('=====================');
  
  const allTests = [
    { name: 'Environment Variables', passed: envTestPassed },
    { name: 'Module Imports', passed: importTestPassed },
    { name: 'Configuration', passed: configTestPassed },
    { name: 'Mock Data Generation', passed: mockDataTestPassed },
    { name: 'API Structure', passed: apiTestPassed }
  ];
  
  const passedTests = allTests.filter(test => test.passed).length;
  const totalTests = allTests.length;
  
  allTests.forEach(test => {
    console.log(`${test.passed ? '✅' : '❌'} ${test.name}`);
  });
  
  console.log(`\n📊 Overall Result: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! Ready for MongoDB integration.');
    console.log('\n🚀 Next Steps:');
    console.log('1. Install MongoDB (Docker or local)');
    console.log('2. Run: npm run setup:mock');
    console.log('3. Run: npm run test:mongodb');
    console.log('4. Run: npm run cron:dev');
  } else {
    console.log('⚠️ Some tests failed. Please fix the issues before proceeding.');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// Run the test
testBasic();
