import React, { useState, useEffect } from 'react';
import { X, Share2, CreditCard, Globe, Eye, Smartphone, Search, AlertCircle, CheckCircle } from 'lucide-react';
import { businessManagerApi } from '../utils/businessManagerApi';
import { BusinessManager, AssetSharingRequest } from '../types/facebook';

interface AssetSharingModalProps {
  isOpen: boolean;
  onClose: () => void;
  assetId: string;
  assetType: 'AD_ACCOUNT' | 'PAGE' | 'PIXEL' | 'APP';
  assetName: string;
  currentBusinessId: string;
}

const ASSET_TYPE_LABELS = {
  AD_ACCOUNT: 'Tài khoản quảng cáo',
  PAGE: 'Fanpage',
  PIXEL: 'Pixel',
  APP: 'Ứng dụng'
};

const ASSET_TYPE_ICONS = {
  AD_ACCOUNT: CreditCard,
  PAGE: Globe,
  PIXEL: Eye,
  APP: Smartphone
};

export const AssetSharingModal: React.FC<AssetSharingModalProps> = ({
  isOpen,
  onClose,
  assetId,
  assetType,
  assetName,
  currentBusinessId
}) => {
  const [businessManagers, setBusinessManagers] = useState<BusinessManager[]>([]);
  const [selectedBusinessId, setSelectedBusinessId] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [sharingRequest, setSharingRequest] = useState<AssetSharingRequest | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadBusinessManagers();
    }
  }, [isOpen]);

  const loadBusinessManagers = async () => {
    setIsLoading(true);
    try {
      const bms = await businessManagerApi.getBusinessManagers();
      // Filter out current business manager
      const otherBMs = bms.filter(bm => bm.id !== currentBusinessId);
      setBusinessManagers(otherBMs);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load business managers');
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async () => {
    if (!selectedBusinessId) {
      setError('Vui lòng chọn Business Manager để chia sẻ');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const request = await businessManagerApi.shareAsset(
        currentBusinessId,
        assetId,
        assetType,
        selectedBusinessId
      );

      setSharingRequest(request);
      setSuccess(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Không thể chia sẻ tài sản');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setSelectedBusinessId('');
    setSearchTerm('');
    setError(null);
    setSuccess(false);
    setSharingRequest(null);
    onClose();
  };

  const filteredBusinessManagers = businessManagers.filter(bm =>
    bm.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bm.id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const AssetIcon = ASSET_TYPE_ICONS[assetType];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
              <Share2 className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">Chia sẻ tài sản</h2>
              <p className="text-sm text-gray-600">Chia sẻ {ASSET_TYPE_LABELS[assetType].toLowerCase()} với Business Manager khác</p>
            </div>
          </div>
          <button
            onClick={handleClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Asset Info */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <AssetIcon className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900">{assetName}</h3>
                <p className="text-sm text-gray-600">{ASSET_TYPE_LABELS[assetType]} • ID: {assetId}</p>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <span className="text-red-800">{error}</span>
            </div>
          )}

          {success && sharingRequest && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-green-800 font-medium">Yêu cầu chia sẻ đã được gửi!</span>
              </div>
              <p className="text-sm text-green-700">
                ID yêu cầu: {sharingRequest.id} • Trạng thái: {sharingRequest.status}
              </p>
            </div>
          )}

          {!success && (
            <>
              {/* Search */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Tìm kiếm Business Manager
                </label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Tìm theo tên hoặc ID Business Manager..."
                  />
                </div>
              </div>

              {/* Business Manager List */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Chọn Business Manager để chia sẻ ({filteredBusinessManagers.length})
                </label>
                
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span className="ml-2 text-gray-600">Đang tải...</span>
                  </div>
                ) : filteredBusinessManagers.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {searchTerm ? 'Không tìm thấy Business Manager nào' : 'Không có Business Manager khác'}
                  </div>
                ) : (
                  <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
                    {filteredBusinessManagers.map((bm) => (
                      <label
                        key={bm.id}
                        className={`flex items-center p-4 border-b border-gray-100 last:border-b-0 cursor-pointer hover:bg-gray-50 transition-colors ${
                          selectedBusinessId === bm.id ? 'bg-blue-50 border-blue-200' : ''
                        }`}
                      >
                        <input
                          type="radio"
                          name="businessManager"
                          value={bm.id}
                          checked={selectedBusinessId === bm.id}
                          onChange={(e) => setSelectedBusinessId(e.target.value)}
                          className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                        />
                        <div className="ml-3 flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium text-gray-900">{bm.name}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              bm.verification_status === 'verified' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {bm.verification_status}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 font-mono">{bm.id}</p>
                          {bm.primary_page && (
                            <p className="text-xs text-gray-500 mt-1">
                              Page chính: {bm.primary_page.name}
                            </p>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              {success ? 'Đóng' : 'Hủy'}
            </button>
            
            {!success && (
              <button
                onClick={handleShare}
                disabled={isLoading || !selectedBusinessId}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>Đang chia sẻ...</span>
                  </>
                ) : (
                  <>
                    <Share2 className="w-4 h-4" />
                    <span>Chia sẻ</span>
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
