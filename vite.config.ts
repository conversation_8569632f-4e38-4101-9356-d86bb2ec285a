import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  define: {
    global: 'globalThis',
  },
  resolve: {
    alias: {
      // Prevent MongoDB from being bundled in browser
      'mongodb': false,
      'node-cron': false,
      'express': false,
      'cors': false,
    }
  }
});
