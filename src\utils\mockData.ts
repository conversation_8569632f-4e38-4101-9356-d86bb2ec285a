import { 
  AdAccount, 
  Campaign, 
  AdSet, 
  Ad, 
  Insights 
} from '../types/facebook';

export const mockAdAccounts: AdAccount[] = [
  {
    id: 'act_*********',
    name: 'Công ty ABC - T<PERSON><PERSON> kho<PERSON>n chính',
    account_id: '*********',
    currency: 'VND',
    account_status: 1,
    business: {
      id: 'business_123',
      name: 'Công ty ABC'
    }
  },
  {
    id: 'act_*********',
    name: '<PERSON><PERSON><PERSON> hàng XYZ - Tài khoản phụ',
    account_id: '*********',
    currency: 'VND',
    account_status: 1,
    business: {
      id: 'business_456',
      name: '<PERSON><PERSON><PERSON> hàng XYZ'
    }
  }
];

export const mockCampaigns: Campaign[] = [
  {
    id: 'campaign_001',
    name: '<PERSON><PERSON><PERSON> d<PERSON>ch Black Friday 2024',
    status: 'ACTIVE',
    objective: 'CONVERSIONS',
    created_time: '2024-11-01T00:00:00Z',
    updated_time: '2024-12-15T10:30:00Z',
    start_time: '2024-11-20T00:00:00Z',
    stop_time: '2024-11-30T23:59:59Z',
    daily_budget: '********', // 500,000 VND
    lifetime_budget: '1********0' // 15,000,000 VND
  },
  {
    id: 'campaign_002',
    name: 'Quảng cáo sản phẩm mới - Điện thoại',
    status: 'ACTIVE',
    objective: 'REACH',
    created_time: '2024-12-01T00:00:00Z',
    updated_time: '2024-12-17T14:20:00Z',
    start_time: '2024-12-01T00:00:00Z',
    daily_budget: '********', // 300,000 VND
    budget_remaining: '********' // 250,000 VND
  },
  {
    id: 'campaign_003',
    name: 'Tăng nhận diện thương hiệu',
    status: 'PAUSED',
    objective: 'BRAND_AWARENESS',
    created_time: '2024-11-15T00:00:00Z',
    updated_time: '2024-12-10T09:15:00Z',
    start_time: '2024-11-15T00:00:00Z',
    stop_time: '2024-12-31T23:59:59Z',
    lifetime_budget: '10000000000' // 100,000,000 VND
  },
  {
    id: 'campaign_004',
    name: 'Retargeting khách hàng cũ',
    status: 'ACTIVE',
    objective: 'CONVERSIONS',
    created_time: '2024-12-10T00:00:00Z',
    updated_time: '2024-12-17T16:45:00Z',
    start_time: '2024-12-10T00:00:00Z',
    daily_budget: '20000000' // 200,000 VND
  }
];

export const mockAdSets: AdSet[] = [
  {
    id: 'adset_001',
    name: 'Nhóm quảng cáo - Nam 25-35 tuổi',
    status: 'ACTIVE',
    campaign_id: 'campaign_001',
    created_time: '2024-11-01T00:00:00Z',
    updated_time: '2024-12-15T10:30:00Z',
    start_time: '2024-11-20T00:00:00Z',
    end_time: '2024-11-30T23:59:59Z',
    daily_budget: '********', // 250,000 VND
    bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
    optimization_goal: 'OFFSITE_CONVERSIONS'
  },
  {
    id: 'adset_002',
    name: 'Nhóm quảng cáo - Nữ 18-45 tuổi',
    status: 'ACTIVE',
    campaign_id: 'campaign_001',
    created_time: '2024-11-01T00:00:00Z',
    updated_time: '2024-12-15T10:30:00Z',
    start_time: '2024-11-20T00:00:00Z',
    end_time: '2024-11-30T23:59:59Z',
    daily_budget: '********', // 250,000 VND
    bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
    optimization_goal: 'OFFSITE_CONVERSIONS'
  },
  {
    id: 'adset_003',
    name: 'Targeting theo sở thích công nghệ',
    status: 'ACTIVE',
    campaign_id: 'campaign_002',
    created_time: '2024-12-01T00:00:00Z',
    updated_time: '2024-12-17T14:20:00Z',
    start_time: '2024-12-01T00:00:00Z',
    daily_budget: '********', // 300,000 VND
    bid_strategy: 'LOWEST_COST_WITH_BID_CAP',
    optimization_goal: 'REACH'
  },
  {
    id: 'adset_004',
    name: 'Lookalike audience - Khách hàng VIP',
    status: 'PAUSED',
    campaign_id: 'campaign_003',
    created_time: '2024-11-15T00:00:00Z',
    updated_time: '2024-12-10T09:15:00Z',
    start_time: '2024-11-15T00:00:00Z',
    end_time: '2024-12-31T23:59:59Z',
    lifetime_budget: '********00', // 50,000,000 VND
    bid_strategy: 'LOWEST_COST_WITHOUT_CAP',
    optimization_goal: 'BRAND_AWARENESS'
  }
];

export const mockAds: Ad[] = [
  {
    id: 'ad_001',
    name: 'Video quảng cáo Black Friday - Phiên bản A',
    status: 'ACTIVE',
    adset_id: 'adset_001',
    campaign_id: 'campaign_001',
    created_time: '2024-11-01T00:00:00Z',
    updated_time: '2024-12-15T10:30:00Z',
    creative: {
      id: 'creative_001',
      name: 'Video Black Friday Creative',
      object_story_spec: {}
    }
  },
  {
    id: 'ad_002',
    name: 'Hình ảnh quảng cáo Black Friday - Phiên bản B',
    status: 'ACTIVE',
    adset_id: 'adset_001',
    campaign_id: 'campaign_001',
    created_time: '2024-11-01T00:00:00Z',
    updated_time: '2024-12-15T10:30:00Z',
    creative: {
      id: 'creative_002',
      name: 'Image Black Friday Creative',
      object_story_spec: {}
    }
  },
  {
    id: 'ad_003',
    name: 'Carousel sản phẩm điện thoại mới',
    status: 'ACTIVE',
    adset_id: 'adset_003',
    campaign_id: 'campaign_002',
    created_time: '2024-12-01T00:00:00Z',
    updated_time: '2024-12-17T14:20:00Z',
    creative: {
      id: 'creative_003',
      name: 'Phone Carousel Creative',
      object_story_spec: {}
    }
  },
  {
    id: 'ad_004',
    name: 'Video giới thiệu thương hiệu',
    status: 'PAUSED',
    adset_id: 'adset_004',
    campaign_id: 'campaign_003',
    created_time: '2024-11-15T00:00:00Z',
    updated_time: '2024-12-10T09:15:00Z',
    creative: {
      id: 'creative_004',
      name: 'Brand Video Creative',
      object_story_spec: {}
    }
  },
  {
    id: 'ad_005',
    name: 'Retargeting - Sản phẩm đã xem',
    status: 'ACTIVE',
    adset_id: 'adset_001',
    campaign_id: 'campaign_004',
    created_time: '2024-12-10T00:00:00Z',
    updated_time: '2024-12-17T16:45:00Z',
    creative: {
      id: 'creative_005',
      name: 'Retargeting Creative',
      object_story_spec: {}
    }
  }
];

export const mockInsights: Insights[] = [
  {
    date_start: '2024-12-10',
    date_stop: '2024-12-10',
    impressions: '125000',
    clicks: '3250',
    spend: '2500000', // 2,500,000 VND
    reach: '98000',
    frequency: '1.28',
    cpm: '20408.16', // VND
    cpc: '769.23', // VND
    ctr: '2.60',
    cpp: '25.51',
    cost_per_unique_click: '850.00',
    unique_clicks: '2941',
    unique_ctr: '3.00',
    actions: [
      { action_type: 'purchase', value: '45' },
      { action_type: 'add_to_cart', value: '128' },
      { action_type: 'view_content', value: '892' },
      { action_type: 'initiate_checkout', value: '67' }
    ],
    cost_per_action_type: [
      { action_type: 'purchase', value: '55555.56' },
      { action_type: 'add_to_cart', value: '19531.25' }
    ]
  },
  {
    date_start: '2024-12-11',
    date_stop: '2024-12-11',
    impressions: '142000',
    clicks: '3890',
    spend: '2800000', // 2,800,000 VND
    reach: '110000',
    frequency: '1.29',
    cpm: '19718.31',
    cpc: '719.54',
    ctr: '2.74',
    cpp: '25.45',
    cost_per_unique_click: '795.45',
    unique_clicks: '3521',
    unique_ctr: '3.20',
    actions: [
      { action_type: 'purchase', value: '52' },
      { action_type: 'add_to_cart', value: '156' },
      { action_type: 'view_content', value: '1024' },
      { action_type: 'initiate_checkout', value: '78' }
    ],
    cost_per_action_type: [
      { action_type: 'purchase', value: '53846.15' },
      { action_type: 'add_to_cart', value: '17948.72' }
    ]
  },
  {
    date_start: '2024-12-12',
    date_stop: '2024-12-12',
    impressions: '158000',
    clicks: '4120',
    spend: '3100000', // 3,100,000 VND
    reach: '118000',
    frequency: '1.34',
    cpm: '19620.25',
    cpc: '752.43',
    ctr: '2.61',
    cpp: '26.27',
    cost_per_unique_click: '820.00',
    unique_clicks: '3780',
    unique_ctr: '3.20',
    actions: [
      { action_type: 'purchase', value: '58' },
      { action_type: 'add_to_cart', value: '172' },
      { action_type: 'view_content', value: '1156' },
      { action_type: 'initiate_checkout', value: '89' }
    ],
    cost_per_action_type: [
      { action_type: 'purchase', value: '53448.28' },
      { action_type: 'add_to_cart', value: '18023.26' }
    ]
  },
  {
    date_start: '2024-12-13',
    date_stop: '2024-12-13',
    impressions: '134000',
    clicks: '3650',
    spend: '2750000', // 2,750,000 VND
    reach: '105000',
    frequency: '1.28',
    cpm: '20522.39',
    cpc: '753.42',
    ctr: '2.72',
    cpp: '26.19',
    cost_per_unique_click: '825.00',
    unique_clicks: '3333',
    unique_ctr: '3.17',
    actions: [
      { action_type: 'purchase', value: '48' },
      { action_type: 'add_to_cart', value: '145' },
      { action_type: 'view_content', value: '967' },
      { action_type: 'initiate_checkout', value: '72' }
    ],
    cost_per_action_type: [
      { action_type: 'purchase', value: '57291.67' },
      { action_type: 'add_to_cart', value: '18965.52' }
    ]
  },
  {
    date_start: '2024-12-14',
    date_stop: '2024-12-14',
    impressions: '167000',
    clicks: '4450',
    spend: '3350000', // 3,350,000 VND
    reach: '125000',
    frequency: '1.34',
    cpm: '20059.88',
    cpc: '752.81',
    ctr: '2.66',
    cpp: '26.80',
    cost_per_unique_click: '810.00',
    unique_clicks: '4136',
    unique_ctr: '3.31',
    actions: [
      { action_type: 'purchase', value: '62' },
      { action_type: 'add_to_cart', value: '189' },
      { action_type: 'view_content', value: '1234' },
      { action_type: 'initiate_checkout', value: '95' }
    ],
    cost_per_action_type: [
      { action_type: 'purchase', value: '54032.26' },
      { action_type: 'add_to_cart', value: '17724.87' }
    ]
  }
];

// Mock API functions
export const getMockAdAccounts = (): Promise<AdAccount[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockAdAccounts), 800);
  });
};

export const getMockCampaigns = (accountId: string): Promise<Campaign[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockCampaigns), 600);
  });
};

export const getMockAdSets = (accountId: string): Promise<AdSet[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockAdSets), 500);
  });
};

export const getMockAds = (accountId: string): Promise<Ad[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockAds), 400);
  });
};

export const getMockInsights = (objectId: string, level: string, datePreset: string): Promise<Insights[]> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockInsights), 700);
  });
};