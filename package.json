{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "cron:dev": "tsx server/cronServer.ts", "cron:build": "tsc server/cronServer.ts --outDir dist/server", "cron:start": "node dist/server/cronServer.js", "db:setup": "tsx scripts/setupDatabase.ts", "sync:manual": "tsx scripts/manualSync.ts"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "mongodb": "^6.3.0", "express": "^4.18.2", "cors": "^2.8.5", "node-cron": "^3.0.3", "dotenv": "^16.3.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "@types/node": "^20.10.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node-cron": "^3.0.11", "tsx": "^4.7.0"}}