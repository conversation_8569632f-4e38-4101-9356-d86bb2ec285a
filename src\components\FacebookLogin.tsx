import React, { useState } from 'react';
import { LogIn, Loader } from 'lucide-react';
import { facebookApi } from '../utils/facebookApi';
import { FacebookUser } from '../types/facebook';

interface FacebookLoginProps {
  onLoginSuccess: (user: FacebookUser) => void;
  onLoginError: (error: string) => void;
}

export const FacebookLogin: React.FC<FacebookLoginProps> = ({ 
  onLoginSuccess, 
  onLoginError 
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [appId, setAppId] = useState('678866331674054');
  const [useMockData, setUseMockData] = useState(false);

  const handleLogin = async () => {
    setIsLoading(true);

    try {
      if (useMockData) {
        // Mock login for demo purposes
        setTimeout(() => {
          const mockUser = {
            id: 'mock_user_123',
            name: 'Demo User',
            email: '<EMAIL>',
            picture: {
              data: {
                url: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150'
              }
            }
          };
          onLoginSuccess(mockUser);
          setIsLoading(false);
        }, 2000);
      } else {
        // Initialize Facebook SDK
        await facebookApi.initialize(appId);
        
        // Attempt login
        const loginResult = await facebookApi.login();
        
        if (loginResult.success) {
          // Get user information
          const user = await facebookApi.getCurrentUser();
          onLoginSuccess(user);
        } else {
          onLoginError(loginResult.error || 'Login failed');
        }
        setIsLoading(false);
      }
    } catch (error) {
      onLoginError(error instanceof Error ? error.message : 'An error occurred during login');
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-4">
      <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 p-10 w-full max-w-lg">
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
            <LogIn className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
            Facebook Ads Dashboard
          </h1>
          <p className="text-gray-600 text-lg">
            Kết nối tài khoản Facebook để truy cập dữ liệu quảng cáo
          </p>
        </div>

        <div className="space-y-6">
          <div>
            <label htmlFor="appId" className="block text-sm font-semibold text-gray-700 mb-3">
              Facebook App ID
            </label>
            <input
              type="text"
              id="appId"
              value={appId}
              onChange={(e) => setAppId(e.target.value)}
              placeholder="Nhập Facebook App ID của bạn"
              className="w-full px-5 py-4 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-gray-50/50 text-gray-900 font-medium"
              disabled={isLoading}
              readOnly
            />
            <p className="text-xs text-gray-500 mt-2">
              App ID được cấu hình sẵn từ Facebook Developers Console
            </p>
          </div>

          <div className="flex items-center space-x-3 p-4 bg-blue-50 rounded-xl border border-blue-100">
            <input
              type="checkbox"
              id="mockData"
              checked={useMockData}
              onChange={(e) => setUseMockData(e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              disabled={isLoading}
            />
            <label htmlFor="mockData" className="text-sm font-medium text-blue-900">
              Sử dụng dữ liệu demo (không cần tài khoản Facebook thật)
            </label>
          </div>

          <button
            onClick={handleLogin}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
          >
            {isLoading ? (
              <>
                <Loader className="w-6 h-6 animate-spin" />
                <span className="text-lg">Đang kết nối...</span>
              </>
            ) : (
              <>
                <LogIn className="w-6 h-6" />
                <span className="text-lg">
                  {useMockData ? 'Đăng nhập Demo' : 'Kết nối với Facebook'}
                </span>
              </>
            )}
          </button>
        </div>

        <div className="mt-8 p-5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100">
          <h3 className="text-sm font-bold text-blue-900 mb-3">Quyền truy cập cần thiết:</h3>
          <ul className="text-xs text-blue-800 space-y-2">
            <li className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              <span>Đọc dữ liệu quảng cáo (ads_read)</span>
            </li>
            <li className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              <span>Quản lý quảng cáo (ads_management)</span>
            </li>
            <li className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              <span>Truy cập dữ liệu doanh nghiệp (business_management)</span>
            </li>
            <li className="flex items-center space-x-2">
              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              <span>Đọc tương tác trang (pages_read_engagement)</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};