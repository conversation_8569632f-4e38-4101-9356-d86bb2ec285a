import React, { useState } from 'react';
import { FacebookLogin } from './components/FacebookLogin';
import { Dashboard } from './components/Dashboard';
import { FacebookUser } from './types/facebook';

function App() {
  const [user, setUser] = useState<FacebookUser | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleLoginSuccess = (userData: FacebookUser) => {
    setUser(userData);
    setError(null);
  };

  const handleLoginError = (errorMessage: string) => {
    setError(errorMessage);
    setUser(null);
  };

  const handleLogout = () => {
    setUser(null);
    setError(null);
  };

  return (
    <div className="App">
      {error && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-lg z-50">
          <p className="font-medium">Error</p>
          <p className="text-sm">{error}</p>
        </div>
      )}

      {!user ? (
        <FacebookLogin 
          onLoginSuccess={handleLoginSuccess}
          onLoginError={handleLoginError}
        />
      ) : (
        <Dashboard 
          user={user} 
          onLogout={handleLogout}
        />
      )}
    </div>
  );
}

export default App;