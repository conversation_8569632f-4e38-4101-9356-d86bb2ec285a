import React, { useState, useEffect } from 'react';
import { 
  LogOut, 
  RefreshCw, 
  TrendingUp, 
  Eye, 
  MousePointer, 
  DollarSign,
  Users,
  BarChart3,
  Download,
  Filter,
  Settings,
  Bell,
  Search
} from 'lucide-react';
import { facebookApi } from '../utils/facebookApi';
import { 
  FacebookUser, 
  AdAccount, 
  Campaign, 
  AdSet, 
  Ad, 
  Insights 
} from '../types/facebook';
import { CampaignsTable } from './CampaignsTable';
import { AdSetsTable } from './AdSetsTable';
import { AdsTable } from './AdsTable';
import { InsightsChart } from './InsightsChart';
import { MetricsCards } from './MetricsCards';
import { PerformanceAnalyzer } from './PerformanceAnalyzer';

interface DashboardProps {
  user: FacebookUser;
  onLogout: () => void;
}

export const Dashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {
  const [adAccounts, setAdAccounts] = useState<AdAccount[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<AdAccount | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [adSets, setAdSets] = useState<AdSet[]>([]);
  const [ads, setAds] = useState<Ad[]>([]);
  const [insights, setInsights] = useState<Insights[]>([]);
  const [activeTab, setActiveTab] = useState<'campaigns' | 'adsets' | 'ads' | 'insights' | 'analyzer'>('campaigns');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('last_30d');
  const [searchTerm, setSearchTerm] = useState('');
  const [isMockMode] = useState(user.id === 'mock_user_123');

  useEffect(() => {
    if (isMockMode) {
      facebookApi.setMockMode(true);
    }
    loadAdAccounts();
  }, []);

  useEffect(() => {
    if (selectedAccount) {
      loadData();
    }
  }, [selectedAccount, dateRange]);

  const loadAdAccounts = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const accounts = await facebookApi.getAdAccounts();
      setAdAccounts(accounts);
      if (accounts.length > 0) {
        setSelectedAccount(accounts[0]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load ad accounts');
    } finally {
      setIsLoading(false);
    }
  };

  const loadData = async () => {
    if (!selectedAccount) return;

    setIsLoading(true);
    setError(null);

    try {
      const [campaignsData, adSetsData, adsData, insightsData] = await Promise.all([
        facebookApi.getCampaigns(selectedAccount.id),
        facebookApi.getAdSets(selectedAccount.id),
        facebookApi.getAds(selectedAccount.id),
        facebookApi.getInsights(selectedAccount.id, 'account', dateRange)
      ]);

      setCampaigns(campaignsData);
      setAdSets(adSetsData);
      setAds(adsData);
      setInsights(insightsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await facebookApi.logout();
      onLogout();
    } catch (err) {
      console.error('Logout error:', err);
      onLogout();
    }
  };

  const exportData = () => {
    const data = {
      account: selectedAccount,
      campaigns,
      adSets,
      ads,
      insights,
      exportedAt: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `facebook-ads-data-${selectedAccount?.account_id}-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const tabs = [
    { id: 'campaigns' as const, label: 'Chiến dịch', count: campaigns.length, icon: TrendingUp },
    { id: 'adsets' as const, label: 'Nhóm quảng cáo', count: adSets.length, icon: Users },
    { id: 'ads' as const, label: 'Quảng cáo', count: ads.length, icon: Eye },
    { id: 'insights' as const, label: 'Báo cáo', count: insights.length, icon: BarChart3 },
    { id: 'analyzer' as const, label: 'Phân tích AI', count: 0, icon: TrendingUp }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm shadow-lg border-b border-gray-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                <BarChart3 className="w-7 h-7 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Facebook Ads Dashboard
                </h1>
                <p className="text-sm text-gray-600 flex items-center space-x-2">
                  <span>Chào mừng trở lại, {user.name}</span>
                  {isMockMode && (
                    <span className="bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full text-xs font-medium">
                      Demo Mode
                    </span>
                  )}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search */}
              <div className="relative hidden md:block">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Tìm kiếm..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50/50 w-64"
                />
              </div>

              {selectedAccount && (
                <div className="flex items-center space-x-2">
                  <select
                    value={selectedAccount.id}
                    onChange={(e) => {
                      const account = adAccounts.find(acc => acc.id === e.target.value);
                      setSelectedAccount(account || null);
                    }}
                    className="px-4 py-2.5 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 font-medium min-w-[200px]"
                  >
                    {adAccounts.map(account => (
                      <option key={account.id} value={account.id}>
                        {account.name} ({account.account_id})
                      </option>
                    ))}
                  </select>

                  <select
                    value={dateRange}
                    onChange={(e) => setDateRange(e.target.value)}
                    className="px-4 py-2.5 border border-gray-200 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/80 font-medium"
                  >
                    <option value="today">Hôm nay</option>
                    <option value="yesterday">Hôm qua</option>
                    <option value="last_7d">7 ngày qua</option>
                    <option value="last_14d">14 ngày qua</option>
                    <option value="last_30d">30 ngày qua</option>
                    <option value="last_90d">90 ngày qua</option>
                  </select>
                </div>
              )}

              {/* Notifications */}
              <button className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors relative">
                <Bell className="w-5 h-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>

              {/* Settings */}
              <button className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="w-5 h-5" />
              </button>

              <button
                onClick={loadData}
                disabled={isLoading || !selectedAccount}
                className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              >
                <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={exportData}
                disabled={!selectedAccount}
                className="p-2.5 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
              >
                <Download className="w-5 h-5" />
              </button>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2.5 text-gray-700 hover:text-gray-900 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200 font-medium"
              >
                <LogOut className="w-4 h-4" />
                <span>Đăng xuất</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <p className="text-red-700 font-medium">{error}</p>
            </div>
          </div>
        )}

        {selectedAccount && (
          <>
            {/* Metrics Cards */}
            {insights.length > 0 && (
              <MetricsCards insights={insights} />
            )}

            {/* Tabs */}
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50">
              <div className="border-b border-gray-200/50">
                <nav className="-mb-px flex space-x-1 px-6">
                  {tabs.map(tab => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`py-4 px-4 border-b-2 font-semibold text-sm transition-all duration-200 flex items-center space-x-2 ${
                        activeTab === tab.id
                          ? 'border-blue-500 text-blue-600 bg-blue-50/50'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50/50'
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      <span>{tab.label}</span>
                      <span className={`py-0.5 px-2 rounded-full text-xs font-bold ${
                        activeTab === tab.id 
                          ? 'bg-blue-100 text-blue-700' 
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        {tab.count}
                      </span>
                    </button>
                  ))}
                </nav>
              </div>

              <div className="p-8">
                {isLoading ? (
                  <div className="flex flex-col items-center justify-center py-16">
                    <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
                      <RefreshCw className="w-8 h-8 animate-spin text-white" />
                    </div>
                    <span className="text-lg font-medium text-gray-700">Đang tải dữ liệu...</span>
                    <span className="text-sm text-gray-500 mt-1">Vui lòng chờ trong giây lát</span>
                  </div>
                ) : (
                  <>
                    {activeTab === 'campaigns' && <CampaignsTable campaigns={campaigns} />}
                    {activeTab === 'adsets' && <AdSetsTable adSets={adSets} />}
                    {activeTab === 'ads' && <AdsTable ads={ads} />}
                    {activeTab === 'insights' && <InsightsChart insights={insights} />}
                    {activeTab === 'analyzer' && <PerformanceAnalyzer />}
                  </>
                )}
              </div>
            </div>
          </>
        )}

        {!selectedAccount && !isLoading && (
          <div className="text-center py-16">
            <div className="w-20 h-20 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <BarChart3 className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 mb-3">Không có tài khoản quảng cáo</h3>
            <p className="text-gray-600 text-lg">
              Không tìm thấy tài khoản quảng cáo nào cho tài khoản Facebook của bạn.
            </p>
          </div>
        )}
      </main>
    </div>
  );
};