import React, { useState } from 'react';
import { AdSet } from '../types/facebook';
import { Calendar, DollarSign, Target, Play, Pause, Square, BarChart3, TrendingUp } from 'lucide-react';
import { DetailedInsightsModal } from './DetailedInsightsModal';

interface AdSetsTableProps {
  adSets: AdSet[];
  accountId?: string;
}

export const AdSetsTable: React.FC<AdSetsTableProps> = ({ adSets, accountId }) => {
  const [sortField, setSortField] = useState<keyof AdSet>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedAdSet, setSelectedAdSet] = useState<AdSet | null>(null);
  const [showInsightsModal, setShowInsightsModal] = useState(false);

  const handleSort = (field: keyof AdSet) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const sortedAdSets = [...adSets].sort((a, b) => {
    const aValue = a[sortField] || '';
    const bValue = b[sortField] || '';
    
    if (sortDirection === 'asc') {
      return aValue > bValue ? 1 : -1;
    } else {
      return aValue < bValue ? 1 : -1;
    }
  });

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return <Play className="w-4 h-4 text-green-600" />;
      case 'paused':
        return <Pause className="w-4 h-4 text-yellow-600" />;
      default:
        return <Square className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handleViewInsights = (adSet: AdSet) => {
    setSelectedAdSet(adSet);
    setShowInsightsModal(true);
  };

  if (adSets.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 bg-gradient-to-r from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
          <Target className="w-10 h-10 text-white" />
        </div>
        <h3 className="text-xl font-bold text-gray-900 mb-3">Không tìm thấy nhóm quảng cáo</h3>
        <p className="text-gray-600">
          Không có nhóm quảng cáo nào cho tài khoản đã chọn.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto rounded-xl border border-gray-200">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gradient-to-r from-gray-50 to-blue-50">
          <tr>
            <th 
              onClick={() => handleSort('name')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Tên nhóm quảng cáo
            </th>
            <th 
              onClick={() => handleSort('status')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Trạng thái
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Ngân sách
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Tối ưu hóa
            </th>
            <th 
              onClick={() => handleSort('created_time')}
              className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider cursor-pointer hover:bg-blue-100 transition-colors"
            >
              Ngày tạo
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Lịch trình
            </th>
            <th className="px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
              Hành động
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-100">
          {sortedAdSets.map((adSet) => (
            <tr key={adSet.id} className="hover:bg-blue-50/50 transition-colors">
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div>
                    <div className="text-sm font-bold text-gray-900 mb-1">{adSet.name}</div>
                    <div className="text-xs text-gray-500 font-mono">ID: {adSet.id}</div>
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center space-x-2">
                  {getStatusIcon(adSet.status)}
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(adSet.status)}`}>
                    {adSet.status}
                  </span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center space-x-1">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <div>
                    {adSet.daily_budget && <div className="font-medium">Hàng ngày: {(parseFloat(adSet.daily_budget) / 100).toLocaleString('vi-VN')} ₫</div>}
                    {adSet.lifetime_budget && <div className="font-medium">Tổng: {(parseFloat(adSet.lifetime_budget) / 100).toLocaleString('vi-VN')} ₫</div>}
                    {!adSet.daily_budget && !adSet.lifetime_budget && <span className="text-gray-500">Chưa thiết lập</span>}
                  </div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  <div className="font-bold text-blue-600">{adSet.optimization_goal || 'Chưa thiết lập'}</div>
                  <div className="text-gray-500 text-xs">{adSet.bid_strategy || 'Chưa thiết lập'}</div>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div className="flex items-center space-x-1">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="font-medium">{new Date(adSet.created_time).toLocaleDateString('vi-VN')}</span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div>
                  {adSet.start_time && (
                    <div className="font-medium">Bắt đầu: {new Date(adSet.start_time).toLocaleDateString('vi-VN')}</div>
                  )}
                  {adSet.end_time && (
                    <div className="font-medium">Kết thúc: {new Date(adSet.end_time).toLocaleDateString('vi-VN')}</div>
                  )}
                  {!adSet.start_time && !adSet.end_time && (
                    <span className="text-gray-500">Đang chạy</span>
                  )}
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleViewInsights(adSet)}
                    className="inline-flex items-center px-3 py-1.5 bg-blue-600 text-white text-xs font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    title="Xem chi tiết hiệu suất"
                  >
                    <BarChart3 className="w-3 h-3 mr-1" />
                    Chi tiết
                  </button>
                  <button
                    className="inline-flex items-center px-3 py-1.5 bg-green-600 text-white text-xs font-medium rounded-lg hover:bg-green-700 transition-colors"
                    title="Phân tích hiệu suất"
                  >
                    <TrendingUp className="w-3 h-3 mr-1" />
                    Phân tích
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>

    {/* Detailed Insights Modal */}
    {selectedAdSet && accountId && (
      <DetailedInsightsModal
        isOpen={showInsightsModal}
        onClose={() => {
          setShowInsightsModal(false);
          setSelectedAdSet(null);
        }}
        itemId={selectedAdSet.id}
        itemName={selectedAdSet.name}
        itemType="adset"
        accountId={accountId}
      />
    )}
  </div>
  );
};