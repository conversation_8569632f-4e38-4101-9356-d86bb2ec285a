import { 
  FacebookUser, 
  AdAccount, 
  Campaign, 
  AdSet, 
  Ad, 
  Insights, 
  FacebookApiResponse 
} from '../types/facebook';
import { 
  getMockAdAccounts, 
  getMockCampaigns, 
  getMockAdSets, 
  getMockAds, 
  getMockInsights 
} from './mockData';

declare global {
  interface Window {
    FB: any;
    fbAsyncInit: () => void;
  }
}

class FacebookApiService {
  private accessToken: string | null = null;
  private isInitialized = false;
  private useMockData = false;

  setMockMode(enabled: boolean): void {
    this.useMockData = enabled;
  }

  async initialize(appId: string): Promise<void> {
    if (this.useMockData) {
      this.isInitialized = true;
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      window.fbAsyncInit = () => {
        window.FB.init({
          appId: appId,
          cookie: true,
          xfbml: true,
          version: 'v18.0'
        });
        this.isInitialized = true;
        resolve();
      };

      // Load Facebook SDK
      if (!document.getElementById('facebook-jssdk')) {
        const js = document.createElement('script');
        js.id = 'facebook-jssdk';
        js.src = 'https://connect.facebook.net/en_US/sdk.js';
        document.getElementsByTagName('head')[0].appendChild(js);
      } else if (window.FB) {
        this.isInitialized = true;
        resolve();
      }
    });
  }

  async login(): Promise<{ success: boolean; accessToken?: string; error?: string }> {
    if (this.useMockData) {
      this.accessToken = 'mock_access_token';
      return Promise.resolve({ success: true, accessToken: this.accessToken });
    }

    if (!this.isInitialized) {
      throw new Error('Facebook SDK not initialized');
    }

    return new Promise((resolve) => {
      window.FB.login((response: any) => {
        if (response.authResponse) {
          this.accessToken = response.authResponse.accessToken;
          resolve({ success: true, accessToken: this.accessToken });
        } else {
          resolve({ success: false, error: 'User cancelled login or did not fully authorize.' });
        }
      }, { 
        scope: 'ads_read,ads_management,business_management,pages_read_engagement' 
      });
    });
  }

  async logout(): Promise<void> {
    if (this.useMockData) {
      this.accessToken = null;
      return Promise.resolve();
    }

    return new Promise((resolve) => {
      window.FB.logout(() => {
        this.accessToken = null;
        resolve();
      });
    });
  }

  async getCurrentUser(): Promise<FacebookUser> {
    if (this.useMockData) {
      return Promise.resolve({
        id: 'mock_user_123',
        name: 'Demo User',
        email: '<EMAIL>',
        picture: {
          data: {
            url: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=150'
          }
        }
      });
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api('/me', { fields: 'id,name,email,picture' }, (response: any) => {
        if (response && !response.error) {
          resolve(response);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch user data'));
        }
      });
    });
  }

  async getAdAccounts(): Promise<AdAccount[]> {
    if (this.useMockData) {
      return getMockAdAccounts();
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api('/me/adaccounts', {
        fields: 'id,name,account_id,currency,account_status,business'
      }, (response: FacebookApiResponse<AdAccount>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch ad accounts'));
        }
      });
    });
  }

  async getCampaigns(accountId: string): Promise<Campaign[]> {
    if (this.useMockData) {
      return getMockCampaigns(accountId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${accountId}/campaigns`, {
        fields: 'id,name,status,objective,created_time,updated_time,start_time,stop_time,budget_remaining,daily_budget,lifetime_budget'
      }, (response: FacebookApiResponse<Campaign>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch campaigns'));
        }
      });
    });
  }

  async getAdSets(accountId: string, campaignId?: string): Promise<AdSet[]> {
    if (this.useMockData) {
      return getMockAdSets(accountId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const endpoint = campaignId ? `/${campaignId}/adsets` : `/${accountId}/adsets`;

    return new Promise((resolve, reject) => {
      window.FB.api(endpoint, {
        fields: 'id,name,status,campaign_id,created_time,updated_time,start_time,end_time,daily_budget,lifetime_budget,bid_strategy,optimization_goal'
      }, (response: FacebookApiResponse<AdSet>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch ad sets'));
        }
      });
    });
  }

  async getAds(accountId: string, adSetId?: string): Promise<Ad[]> {
    if (this.useMockData) {
      return getMockAds(accountId);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    const endpoint = adSetId ? `/${adSetId}/ads` : `/${accountId}/ads`;

    return new Promise((resolve, reject) => {
      window.FB.api(endpoint, {
        fields: 'id,name,status,adset_id,campaign_id,created_time,updated_time,creative'
      }, (response: FacebookApiResponse<Ad>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch ads'));
        }
      });
    });
  }

  async getInsights(
    objectId: string, 
    level: 'account' | 'campaign' | 'adset' | 'ad' = 'account',
    datePreset: string = 'last_30d'
  ): Promise<Insights[]> {
    if (this.useMockData) {
      return getMockInsights(objectId, level, datePreset);
    }

    if (!this.accessToken) {
      throw new Error('No access token available');
    }

    return new Promise((resolve, reject) => {
      window.FB.api(`/${objectId}/insights`, {
        level: level,
        date_preset: datePreset,
        fields: 'date_start,date_stop,impressions,clicks,spend,reach,frequency,cpm,cpc,ctr,cpp,cost_per_unique_click,unique_clicks,unique_ctr,actions,cost_per_action_type'
      }, (response: FacebookApiResponse<Insights>) => {
        if (response && !response.error) {
          resolve(response.data);
        } else {
          reject(new Error(response.error?.message || 'Failed to fetch insights'));
        }
      });
    });
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }

  setAccessToken(token: string): void {
    this.accessToken = token;
  }
}

export const facebookApi = new FacebookApiService();