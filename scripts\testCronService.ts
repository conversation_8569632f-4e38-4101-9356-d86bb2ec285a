import { createCronSyncService, defaultSyncConfig } from '../src/services/cronSyncService';
import { mongoDbService } from '../src/utils/mongoDbService';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Test Cron Service
async function testCronService() {
  console.log('🔍 Testing Cron Sync Service...');
  
  try {
    // Connect to MongoDB
    await mongoDbService.connect();
    console.log('✅ Connected to MongoDB successfully!');
    
    // Create indexes
    await mongoDbService.createIndexes();
    console.log('✅ Created indexes successfully!');
    
    // Create test config
    const testConfig = {
      ...defaultSyncConfig,
      accountIds: (process.env.FACEBOOK_ACCOUNT_IDS || 'act_123456789').split(','),
      accessToken: process.env.FACEBOOK_ACCESS_TOKEN || 'test_token',
      syncIntervalHours: 1,
      maxDaysHistory: 7,
      enabledSyncTypes: ['campaigns', 'adsets', 'ads']
    };
    
    console.log('📊 Test Config:', JSON.stringify(testConfig, null, 2));
    
    // Create cron service
    const cronService = createCronSyncService(testConfig);
    console.log('✅ Created Cron Service successfully!');
    
    // Get health status
    const healthStatus = await cronService.getHealthStatus();
    console.log('📊 Cron Service Health Status:', JSON.stringify(healthStatus, null, 2));
    
    // Start manual sync for test account
    console.log('🔄 Starting manual sync for test account...');
    
    // Use mock mode for testing
    if (!process.env.FACEBOOK_ACCESS_TOKEN || process.env.FACEBOOK_ACCESS_TOKEN === 'test_token') {
      console.log('⚠️ Using mock mode since no valid access token provided');
      // In a real implementation, we would use mock data here
    }
    
    // Trigger manual sync
    try {
      await cronService.manualSync(testConfig.accountIds[0]);
      console.log('✅ Manual sync completed successfully!');
    } catch (error) {
      console.warn('⚠️ Manual sync failed (expected in test mode):', error.message);
    }
    
    // Get last sync status
    const syncStatus = await cronService.getLastSyncStatus(testConfig.accountIds[0]);
    console.log('📊 Last Sync Status:', JSON.stringify(syncStatus, null, 2));
    
    // Stop cron service
    await cronService.stop();
    console.log('✅ Stopped Cron Service successfully!');
    
    // Disconnect from MongoDB
    await mongoDbService.disconnect();
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Cron Service test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testCronService();
